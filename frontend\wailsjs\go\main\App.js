// @ts-check
// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT

export function CheckAllSSHAlive(arg1) {
  return window['go']['main']['App']['CheckAllSSHAlive'](arg1);
}

export function CheckCmdIsContinuous(arg1) {
  return window['go']['main']['App']['CheckCmdIsContinuous'](arg1);
}

export function ConnectSSH(arg1, arg2, arg3, arg4, arg5, arg6) {
  return window['go']['main']['App']['ConnectSSH'](arg1, arg2, arg3, arg4, arg5, arg6);
}

export function DeleteSSHHost(arg1, arg2) {
  return window['go']['main']['App']['DeleteSSHHost'](arg1, arg2);
}

export function DisconnectSSH(arg1) {
  return window['go']['main']['App']['DisconnectSSH'](arg1);
}

export function GetConnectedSSHHost() {
  return window['go']['main']['App']['GetConnectedSSHHost']();
}

export function GetHostInfo(arg1, arg2) {
  return window['go']['main']['App']['GetHostInfo'](arg1, arg2);
}

export function StartLogStream(arg1, arg2, arg3) {
  return window['go']['main']['App']['StartLogStream'](arg1, arg2, arg3);
}

export function StopLogStream(arg1) {
  return window['go']['main']['App']['StopLogStream'](arg1);
}
