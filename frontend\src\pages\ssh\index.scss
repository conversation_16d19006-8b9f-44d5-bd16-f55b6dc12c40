// 需要GPU支持动画, 损害性能, 默认关闭

// @keyframes float {
//     0% {
//         transform: translateY(0);
//     }
//     50% {
//         transform: translateY(-5px);
//     }
//     100% {
//         transform: translateY(0);
//     }
// }

// @keyframes sci-fi-gradient {
//     0% {
//         background-position: 0% 50%;
//     }

//     50% {
//         background-position: 100% 50%;
//     }

//     100% {
//         background-position: 0% 50%;
//     }
// }

#app {
    height: 100vh;
    text-align: center;
    overflow: hidden;
}

#logo {
    display: block;
    width: 50%;
    height: 50%;
    margin: auto;
    padding: 10% 0 0;
    background-position: center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-origin: content-box;
}

.result {
    height: 20px;
    line-height: 20px;
    margin: 1.5rem auto;
}

#header {
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #0f0c29 0%, #5b632b 25%, #24243e 50%, #be80c5 75%, #3a7bd5 100%);
    animation: sci-fi-gradient 10s ease infinite;
    background-size: 200% 200%;
}

.ant-menu-item-selected {
    background-color: rgba(255, 255, 255, 0.2) !important; /* 半透明背景 */
    font-weight: 600 !important;
    font-size: 16px !important;
    color: #0e1b0d !important;
    /* 半透明背景 */
    backdrop-filter: blur(10px) !important;
    /* 磨砂效果 */
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    /* 边框 */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

.menu-area {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 0;

    #menu_item {
        &:hover {
            transform: scale(1.05); /* 放大效果 */
            backdrop-filter: blur(15px); /* 增加模糊度 */
            box-shadow: 0 8px 12px rgba(0, 0, 0, 0.2); /* 增强阴影 */
            background: rgba(255, 255, 255, 0.3); /* 提高背景透明度 */
            animation: float 2s ease-in-out infinite; /* 悬浮时浮动效果 */
        }
    }
}

.body-area {
    height: calc(100vh - 70px); /* Subtract header height and some extra space */
    padding: 5px 12px;
    background: rgba(255, 255, 255, 0.2);
    /* 半透明背景 */
    backdrop-filter: blur(10px);
    /* 磨砂效果 */
    border: 1px solid rgba(255, 255, 255, 0.3);
    /* 边框 */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.input-box {
    .btn {
        width: 60px;
        height: 30px;
        line-height: 30px;
        border-radius: 3px;
        border: none;
        margin: 0 0 0 20px;
        padding: 0 8px;
        cursor: pointer;
    }

    .btn:hover {
        background-image: linear-gradient(to top, #cfd9df 0%, #e2ebf0 100%);
        color: #333333;
    }

    .input {
        border: none;
        border-radius: 3px;
        outline: none;
        height: 30px;
        line-height: 30px;
        padding: 0 10px;
        background-color: rgba(240, 240, 240, 1);
        -webkit-font-smoothing: antialiased;
    }

    .input:hover {
        border: none;
        background-color: rgba(255, 255, 255, 1);
    }

    .input:focus {
        border: none;
        background-color: rgba(255, 255, 255, 1);
    }
}

.input_form {
    flex: 0 0 auto;
    margin-bottom: 5px;
}

.input_area {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

label {
    white-space: nowrap;
    margin-right: 10px;
}

.ant-space-item {
    width: 20%;
}

.ant-space-item:nth-last-child(2) {
    width: unset;
    justify-content: unset;
}

// /* Ensure all inputs have the same width */
// .ant-space-item .ant-input {
//     width: 100%;
// }

// /* Make input fields more compact */
// .ant-input {
//     height: 30px;
//     padding: 4px 11px;
//     border-radius: 2px;
// }

// .ant-btn {
//     height: 30px;
//     padding: 0px 15px;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     border-radius: 2px;
// }

// /* Style for primary buttons */
// .ant-btn-primary {
//     background-color: #1890ff;
// }

// /* Style for switches */
// .ant-switch {
//     min-width: 40px;
// }

.ant-space-item:last-child {
    text-align: left;
}

// /* Make sure Ant Design Tabs content takes full height */
// .ant-tabs {
//     display: flex;
//     flex-direction: column;
//     height: calc(100vh - 70px); /* Match body-area height */
// }

.ant-tabs-content {
    flex: 1;
    height: 100%;
}

.ant-tabs-tabpane {
    height: 100%;
}

.ant-tabs-nav {
    margin: 0 !important;
}

.command {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 50%;

    .switch {
        display: flex;
        align-items: center;
        margin: 0 15px;
    }

    .command-input-container {
        position: relative;
        flex: 1;

        .command-history-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            max-height: 200px;
            overflow-y: auto;
            background-color: white;
            border: 1px solid #d9d9d9;
            border-radius: 0 0 4px 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            margin-top: 2px; /* 添加一点间距 */

            .command-history-item {
                padding: 8px 12px;
                cursor: pointer;
                transition: background-color 0.3s;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                text-align: left;

                &:hover {
                    background-color: #f5f5f5;
                }

                &.command-history-empty {
                    color: #999;
                    cursor: default;
                    text-align: center;

                    &:hover {
                        background-color: transparent;
                    }
                }
            }
        }
    }
}

.host-list-item:hover {
    color: brown;
    font-weight: bolder;
}

#editor-log {
    overflow-y: scroll;
    border: 1px solid #ccc;
    padding: 10px;
    background: #f9f9f9;
    flex: 1 1 0%;
}

// #editor-body {
//     position: absolute;
//     top: 0;
//     right: 0;
//     bottom: 0;
//     left: 0;
//     height: 100%;
//     width: 100%;
// }

// .title-bar {
//     // background: #f2f2f2;
//     // border-bottom: 1px solid #ccc;
//     height: 20px;
//     padding: 0 8px;
//     position: absolute;
//     top: 0;
//     left: 0;
//     right: 0;
//     display: flex;
//     align-items: center;
//     justify-content: flex-start;

//     .button {
//         width: 12px;
//         height: 12px;
//         border-radius: 50%;
//         margin-right: 6px;
//     }

//     .red {
//         background: #ff5f57;
//     }
//     .yellow {
//         background: #feca57;
//     }
//     .green {
//         background: #25cc60;
//     }
// }

.delete-host-btn {
    // opacity: 0.6;
    // transition: all 0.3s;
    // background-color: rgba(245, 245, 245, 0.7);
    // border-radius: 4px;

    &:hover {
        opacity: 1;
        background-color: rgba(255, 232, 232, 0.7);
        transform: scale(1.2);
    }
}

.custom-scrollbar {
    &::-webkit-scrollbar {
        display: none;
    }
}

.filter_area {
    display: flex;
    align-items: center;
    margin-right: 15px;
}

.search-result-count {
    font-size: 13px;
    color: #52c41a;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    padding: 2px 8px;
    border-radius: 4px;
    margin-right: 10px;
    display: inline-flex;
    align-items: center;
    height: 28px;

    strong {
        font-weight: 600;
        margin: 0 2px;
    }
}