import QueryRules from '../eums'

export interface SSHConfig {
  username: string
  password: string
  host: string
  port: number
  command: string
  stream: boolean
}

export interface SSHSession {
  id: string
  config: SSHConfig
  logs: string[]
  searchTerm: string
  error: string | null
  isStreaming: boolean
  isConnected: boolean
  selectedRule: QueryRules[]
  store: boolean
  hostportList: string[]
  filtered: boolean
  enableStream: boolean
  aliveStatus: Record<string, boolean>
}

export interface SSHSessionPanelProps {
  session: SSHSession
  setSession: (patch: Partial<SSHSession>) => void
  setActiveSessionId: (id: string) => void
  activeSessionId?: string
}
