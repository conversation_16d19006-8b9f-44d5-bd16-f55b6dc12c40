import globals from 'globals'
import pluginJs from '@eslint/js'
import tseslint from 'typescript-eslint'
import { configs as tsEslintConfigs } from "@typescript-eslint/eslint-plugin";
import pluginReact from 'eslint-plugin-react'
import eslintPlugin<PERSON>rettier from 'eslint-plugin-prettier'
import { env } from 'process'

/** @type {import('eslint').Linter.Config[]} */
export default [
  { files: ['**/*.{js,mjs,cjs,ts,jsx,tsx}'] },
  {
    languageOptions: {
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
        ecmaVersion: 2018,
        sourceType: 'module',
      },
      globals: {
        ...globals.browser,
      },
    },
  },
  pluginJs.configs.recommended,
  ...tseslint.configs.recommended,
  pluginReact.configs.flat.recommended,
  {
    plugins: {
      prettier: eslintPluginPrettier,
      react: pluginReact,
    },
  },
  {
    rules: {
      'no-empty': 'off',
      'no-case-declarations': 'off',
      'no-constant-condition': 'off',
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      '@typescript-eslint/no-this-alias': 'off',
      'no-console': env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-debugger': env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-empty-function': 'off',
      '@typescript-eslint/no-empty-function': ['off'],
      'prettier/prettier': [
        'warn',
        {
          singleQuote: true,
          semi: false,
          trailingComma: 'all',
          endOfLine: 'auto',
        },
      ],
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/camelcase': 'off',
      '@typescript-eslint/no-var-requires': 'off',
      '@typescript-eslint/interface-name-prefix': 'off',
      'no-useless-escape': 0,
      'no-unused-expressions': 0,
      'no-useless-constructor': 0,
      '@typescript-eslint/no-useless-constructor': 'error',
      indent: 'off',
      '@typescript-eslint/ban-types': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      eqeqeq: 0,
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      // suppress errors for missing 'import React' in files
      'react/react-in-jsx-scope': 'off',
      // allow jsx syntax in js files (for next.js project)
      'react/jsx-filename-extension': [
        1,
        { extensions: ['.js', '.jsx', '.ts', '.tsx'] },
      ],
      '@typescript-eslint/no-namespace': 'off',
    },
    settings: {
      react: {
        version: 'detect',
      },
    },
  },
]
