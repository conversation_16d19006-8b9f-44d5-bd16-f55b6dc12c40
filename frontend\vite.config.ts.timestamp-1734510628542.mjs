// vite.config.ts
import { defineConfig } from "file:///F:/env/go/src/wails_app/developer_tools/frontend/node_modules/.pnpm/vite@3.2.11_@types+node@22.10.0/node_modules/vite/dist/node/index.js";
import react from "file:///F:/env/go/src/wails_app/developer_tools/frontend/node_modules/.pnpm/@vitejs+plugin-react@2.2.0_vite@3.2.11_@types+node@22.10.0_/node_modules/@vitejs/plugin-react/dist/index.mjs";
var vite_config_default = defineConfig({
  plugins: [react()]
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcudHMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJGOlxcXFxlbnZcXFxcZ29cXFxcc3JjXFxcXHdhaWxzX2FwcFxcXFxzaWFjdF90b29sc1xcXFxmcm9udGVuZFwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiRjpcXFxcZW52XFxcXGdvXFxcXHNyY1xcXFx3YWlsc19hcHBcXFxcc2lhY3RfdG9vbHNcXFxcZnJvbnRlbmRcXFxcdml0ZS5jb25maWcudHNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0Y6L2Vudi9nby9zcmMvd2FpbHNfYXBwL3NpYWN0X3Rvb2xzL2Zyb250ZW5kL3ZpdGUuY29uZmlnLnRzXCI7aW1wb3J0IHtkZWZpbmVDb25maWd9IGZyb20gJ3ZpdGUnXG5pbXBvcnQgcmVhY3QgZnJvbSAnQHZpdGVqcy9wbHVnaW4tcmVhY3QnXG5cbi8vIGh0dHBzOi8vdml0ZWpzLmRldi9jb25maWcvXG5leHBvcnQgZGVmYXVsdCBkZWZpbmVDb25maWcoe1xuICBwbHVnaW5zOiBbcmVhY3QoKV1cbn0pXG4iXSwKICAibWFwcGluZ3MiOiAiO0FBQW9VLFNBQVEsb0JBQW1CO0FBQy9WLE9BQU8sV0FBVztBQUdsQixJQUFPLHNCQUFRLGFBQWE7QUFBQSxFQUMxQixTQUFTLENBQUMsTUFBTSxDQUFDO0FBQ25CLENBQUM7IiwKICAibmFtZXMiOiBbXQp9Cg==
