/* eslint-disable react/prop-types */
import React, { useState } from 'react'
import ReactMarkdown from 'react-markdown'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { solarizedlight } from 'react-syntax-highlighter/dist/esm/styles/prism'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import 'katex/dist/katex.min.css'
import { CopyToClipboard } from 'react-copy-to-clipboard'
import { Tooltip, message, Button } from 'antd'
import { CopyOutlined, CheckOutlined } from '@ant-design/icons'

import './index.scss'
import './notion.scss'
import './github.scss'
import './material.scss'

const THEMES = ['github', 'notion', 'material']

interface CodeBlockProps {
  node?: any
  inline?: boolean
  className?: string
  children?: React.ReactNode
}

interface AIMarkdownRenderProps {
  content: string
  className?: string
}

const AIMarkdownRender: React.FC<AIMarkdownRenderProps> = ({
  content,
  className,
}) => {
  const [theme, setTheme] = useState<string>('github')

  const CodeBlock = ({
    node,
    inline,
    className,
    children,
    ...props
  }: CodeBlockProps) => {
    const [copied, setCopied] = useState(false)
    const match = /language-(\w+)/.exec(className || '')
    const language = match ? match[1] : ''

    // Extract file path if available (format: language:filepath)
    const filePathMatch = language ? language.split(':') : []
    const actualLanguage =
      filePathMatch.length > 1 ? filePathMatch[0] : language
    const filePath = filePathMatch.length > 1 ? filePathMatch[1] : ''

    const handleCopy = () => {
      setCopied(true)
      message.success('Code copied to clipboard!')
      setTimeout(() => setCopied(false), 2000)
    }

    if (!inline) {
      return (
        <div className={`code-block-container ${theme}-theme`}>
          {filePath && (
            <div className="code-block-filepath">
              <span>{filePath}</span>
            </div>
          )}
          <div className="code-block-header">
            <span className="code-language-label">{actualLanguage}</span>
            <CopyToClipboard text={String(children).replace(/\n$/, '')}>
              <Tooltip title={copied ? 'Copied!' : 'Copy code'}>
                <button className="copy-button" onClick={handleCopy}>
                  {copied ? <CheckOutlined /> : <CopyOutlined />}
                </button>
              </Tooltip>
            </CopyToClipboard>
          </div>
          <SyntaxHighlighter
            style={solarizedlight}
            language={actualLanguage}
            PreTag="div"
            wrapLines={true}
            showLineNumbers={true}
            {...props}
          >
            {String(children).replace(/\n$/, '')}
          </SyntaxHighlighter>
        </div>
      )
    }

    return (
      <code className={`inline-code ${className}`} {...props}>
        {children}
      </code>
    )
  }

  return (
    <div className={`ai-markdown-container ${theme}-theme ${className || ''}`}>
      <div className="theme-switcher">
        <strong>Theme:</strong>
        {THEMES.map((t) => (
          <Button
            key={t}
            size="small"
            type={theme === t ? 'primary' : 'default'}
            onClick={() => setTheme(t)}
            style={{ marginLeft: 8 }}
          >
            {t.charAt(0).toUpperCase() + t.slice(1)}
          </Button>
        ))}
      </div>

      <ReactMarkdown
        remarkPlugins={[remarkGfm, remarkMath]}
        rehypePlugins={[rehypeKatex]}
        components={{
          code: CodeBlock,
          a: ({ node, ...props }) => (
            <a
              target="_blank"
              rel="noopener noreferrer"
              className="markdown-link"
              {...props}
            />
          ),
          table: ({ node, ...props }) => (
            <div className="table-container">
              <table className="markdown-table" {...props} />
            </div>
          ),
          img: ({ node, ...props }) => (
            <img
              className="markdown-image"
              {...props}
              alt={props.alt || 'Image'}
            />
          ),
          blockquote: ({ node, ...props }) => (
            <blockquote className="markdown-blockquote" {...props} />
          ),
          h1: ({ node, ...props }) => (
            <h1 className="markdown-heading markdown-h1" {...props} />
          ),
          h2: ({ node, ...props }) => (
            <h2 className="markdown-heading markdown-h2" {...props} />
          ),
          h3: ({ node, ...props }) => (
            <h3 className="markdown-heading markdown-h3" {...props} />
          ),
          h4: ({ node, ...props }) => (
            <h4 className="markdown-heading markdown-h4" {...props} />
          ),
          h5: ({ node, ...props }) => (
            <h5 className="markdown-heading markdown-h5" {...props} />
          ),
          h6: ({ node, ...props }) => (
            <h6 className="markdown-heading markdown-h6" {...props} />
          ),
          ul: ({ node, ...props }) => (
            <ul className="markdown-list markdown-ul" {...props} />
          ),
          ol: ({ node, ...props }) => (
            <ol className="markdown-list markdown-ol" {...props} />
          ),
          li: ({ node, ...props }) => (
            <li className="markdown-list-item" {...props} />
          ),
          p: ({ node, ...props }) => (
            <p className="markdown-paragraph" {...props} />
          ),
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  )
}

export default AIMarkdownRender
