import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react'
import {
  Layout,
  Input,
  Button,
  message,
  Alert,
  Switch,
  Space,
  Select,
  notification,
  Popover,
  Divider,
  SelectProps,
  Modal,
  Tabs,
} from 'antd'
import { EventsOn, EventsOff } from '../../../wailsjs/runtime'
import {
  ConnectSSH,
  DisconnectSSH,
  StartLogStream,
  StopLogStream,
  GetConnectedSSHHost,
  GetHostInfo,
  CheckCmdIsContinuous,
  DeleteSSHHost,
} from '../../../wailsjs/go/main/App'
import Editor from 'react-simple-code-editor'
import { highlight, languages } from 'prismjs'
import 'prismjs/components/prism-clike'
import 'prismjs/components/prism-javascript'
import 'prismjs/themes/prism-tomorrow.css'
import './index.scss'
import type { GetProps } from 'antd'
import init, { filter_logs } from '../../../public/log_filter.js'
import { escapeRegExp, HandleStrings } from '../../utils'
import QueryRules from '../../eums'
import { DeleteOutlined } from '@ant-design/icons'
import { splitStringAndHandle } from './utils'
import CheckHostsAliveButton from './components/ButtonAlive'
import SearchComponent from './components/Search'
import { v4 as uuidv4 } from 'uuid'
import { useScrollListener } from '../../hooks/useScrollListener'
import { SSHSession, SSHSessionPanelProps } from '../../types'
import { SSHEnum } from '../../events'

const { Content } = Layout

type SearchProps = GetProps<typeof Input.Search>

// 自定义日志高亮规则
languages.log = {
  keyword: {
    pattern:
      /\[ERROR\]|\|.*ERROR.*\||\[INFO\]|\|.*INFO.*\||\[DEBUG\]|\|.*DEBUG.*\||\[WARNING\]|\|.*WARNING.*\|/g,
    alias: 'keyword',
  },
  string: {
    pattern: /".*?"/g,
    alias: 'string',
  },
  number: {
    pattern: /\b\d+\b/g,
    alias: 'number',
  },
}

// 查询配置
const options: SelectProps['options'] = [
  {
    label: '去除前后空格',
    value: 1,
  },
  {
    label: '忽略大小写',
    value: 2,
  },
]

// 多session结构

const MAX_SESSIONS = 10

const SSHComponent: React.FC = () => {
  const [sessions, setSessions] = useState<SSHSession[]>([
    {
      id: uuidv4(),
      config: {
        username: 'root',
        password: 'Dell@123',
        host: '************',
        port: 22,
        command: 'docker ps',
        stream: false,
      },
      logs: [],
      searchTerm: '',
      error: null,
      isStreaming: false,
      isConnected: false,
      selectedRule: [],
      store: false,
      hostportList: [],
      filtered: true,
      enableStream: false,
      aliveStatus: {},
    },
  ])
  const [activeSessionId, setActiveSessionId] = useState(() => sessions[0]?.id)

  // 新建session
  const addSession = () => {
    if (sessions.length >= MAX_SESSIONS) {
      message.warning('最多支持10路SSH连接')
      return
    }
    const newSession: SSHSession = {
      id: uuidv4(),
      config: {
        username: '',
        password: '',
        host: '',
        port: 22,
        command: '',
        stream: false,
      },
      logs: [],
      searchTerm: '',
      error: null,
      isStreaming: false,
      isConnected: false,
      selectedRule: [],
      store: false,
      hostportList: [],
      filtered: true,
      enableStream: false,
      aliveStatus: {},
    }
    setSessions([...sessions, newSession])
    setActiveSessionId(newSession.id)
  }

  // 关闭session
  const removeSession = (id: string) => {
    setSessions((prev) => prev.filter((s) => s.id !== id))
    // 断开后端连接
    DisconnectSSH(id)
    // 切换到剩余第一个
    if (activeSessionId === id && sessions.length > 1) {
      const next = sessions.find((s) => s.id !== id)
      if (next) setActiveSessionId(next.id)
    }
  }

  // 获取当前session
  const currentSession = sessions.find((s) => s.id === activeSessionId)!

  // 事件监听（只监听一次，分发到对应session）
  useEffect(() => {
    const logListener = (payload: any) => {
      // 确保 payload 是对象，并且包含 sessionID
      let sessionID: string, log: string

      if (typeof payload !== 'object' || !payload.sessionID) {
        sessionID = currentSession.id
        log = payload
      } else {
        sessionID = payload.sessionID
        log = payload.log
      }

      // 更新对应会话的日志
      setSessions((prev) => {
        const updatedSessions = prev.map((s) =>
          s.id === sessionID ? { ...s, logs: [...s.logs, log] } : s,
        )
        return updatedSessions
      })
    }

    const errorListener = (payload: any) => {
      console.log('error payload', payload)
      // 确保 payload 是对象，并且包含 sessionID
      let sessionID: string, msg: string

      if (typeof payload !== 'object' || !payload.sessionID) {
        sessionID = currentSession.id
        msg = payload
      } else {
        sessionID = payload.sessionID
        msg = payload.msg
      }

      // 更新对应会话的错误状态
      setSessions((prev) => {
        const updatedSessions = prev.map((s) =>
          s.id === sessionID ? { ...s, error: msg, isStreaming: false } : s,
        )

        // 如果是当前活动的会话，显示错误消息
        if (sessionID === activeSessionId) {
          message.error(msg)
        }

        return updatedSessions
      })
    }

    const commandEndListener = (payload: any) => {
      console.log('command-end payload', payload)
      // 确保 payload 是对象，并且包含 sessionID
      let sessionID: string, msg: string

      if (typeof payload !== 'object' || !payload.sessionID) {
        sessionID = currentSession.id
        msg = payload
      } else {
        sessionID = payload.sessionID
        msg = payload.msg
      }

      // 立即更新对应会话的状态，确保中止按钮正确禁用
      setSessions((prev) => {
        // 查找对应的会话
        const session = prev.find((s) => s.id === sessionID)
        if (!session) {
          console.error(`Session with ID ${sessionID} not found`)
          return prev
        }

        console.log(
          `[${sessionID}] 命令执行完成，设置 isStreaming=false, 当前状态 isStreaming=${session.isStreaming}`,
        )

        // 无论当前状态如何，都将 isStreaming 设置为 false
        const updatedSessions = prev.map((s) => {
          if (s.id === sessionID) {
            // 确保更新 isStreaming 状态
            return {
              ...s,
              isStreaming: false,
              // 如果是流模式，也更新 stream 配置
              config: s.config.stream
                ? { ...s.config, stream: false }
                : s.config,
            }
          }
          return s
        })

        // 如果是当前活动的会话，显示消息
        if (sessionID === activeSessionId) {
          message.info(msg)
        }

        return updatedSessions
      })
    }

    // 注册事件监听器
    EventsOn(SSHEnum.Log, logListener)
    EventsOn(SSHEnum.Error, errorListener)
    EventsOn(SSHEnum.CommandEnd, commandEndListener)

    // 清理函数
    return () => {
      EventsOff(SSHEnum.Log, SSHEnum.Error, SSHEnum.CommandEnd)
    }
  }, [activeSessionId])

  // 适配所有后端调用，带sessionId
  // 例如：ConnectSSH(sessionId, ...)
  // 其余handleXXX、autofillXXX、getHostportList等都要加sessionId
  // ... 其余逻辑同原，每次操作都用currentSession.id

  // 渲染Tabs
  return (
    <Tabs
      type="editable-card"
      activeKey={activeSessionId}
      onChange={(key) => {
        setActiveSessionId(key as string)
        // 切换 tab 时，只需要当前 session 的信息
        const currentSession = sessions.find((s) => s.id === key)
        if (currentSession) {
          // 如果需要，可以在这里加载当前 session 的特定信息
          console.log('currentSession:', currentSession)
        }
      }}
      onEdit={(targetKey, action) => {
        if (action === 'add') addSession()
        else if (action === 'remove') removeSession(targetKey as string)
      }}
      style={{
        height: 'calc(100vh - 70px)',
        display: 'flex',
        flexDirection: 'column',
      }}
      items={sessions.map((session) => ({
        key: session.id,
        label: session.config.host || '新会话',
        children: (
          <SSHSessionPanel
            key={session.id}
            session={session}
            setSession={(patch) =>
              setSessions((prev) =>
                prev.map((s) => (s.id === session.id ? { ...s, ...patch } : s)),
              )
            }
            setActiveSessionId={setActiveSessionId}
            activeSessionId={activeSessionId}
          />
        ),
      }))}
      tabBarExtraContent={
        <Button onClick={addSession} disabled={sessions.length >= MAX_SESSIONS}>
          新建Session
        </Button>
      }
    />
  )
}

const SSHSessionPanel: React.FC<SSHSessionPanelProps> = ({
  session,
  setSession,
  setActiveSessionId, // 保留此参数以便将来使用
  activeSessionId,
}) => {
  // 绑定dom元素
  const editorRef = useRef(null)
  const searchRef = useRef(null)
  const executeBtnRef = useRef(null)
  const [commandHistory, setCommandHistory] = useState<string[]>([])
  const [showCommandHistory, setShowCommandHistory] = useState(false)
  const [filteredHistory, setFilteredHistory] = useState<string[]>([])
  const commandInputRef = useRef<any>(null)
  const historyDropdownRef = useRef<HTMLDivElement>(null)
  const [api, contextHolder] = notification.useNotification()
  const [highlightedCode, setHighlightedCode] = useState('')
  const [filteredLogs, setFilteredLogs] = useState<string[]>([])
  const [visibleLogs, setVisibleLogs] = useState<string[]>([])

  // 获取主机列表 - 使用 useRef 避免依赖循环
  const getHostportListRef = useRef(async () => {
    try {
      const hostportList_ = await GetConnectedSSHHost()
      setSession({ hostportList: hostportList_ })
      if (hostportList_.length === 0) {
        message.info('没有可用的主机记录')
      }
    } catch (error) {
      console.error('获取主机列表失败:', error)
      message.error('获取主机列表失败')
    }
  })

  // 历史命令管理
  useEffect(() => {
    const savedCommands = localStorage.getItem('sshCommandHistory')
    if (savedCommands) {
      try {
        const parsedCommands = JSON.parse(savedCommands)
        setCommandHistory(Array.isArray(parsedCommands) ? parsedCommands : [])
      } catch (e) {
        setCommandHistory([])
      }
    }
  }, [])

  // 组件挂载或激活时加载主机列表 - 单独的 useEffect 避免循环依赖
  useEffect(() => {
    // 只有当前 session 是活动的时才加载主机列表
    if (session.id === activeSessionId) {
      getHostportListRef.current()
    }
  }, [session.id, activeSessionId])

  // 存储历史命令
  const saveCommandToHistory = useCallback(
    (command: string) => {
      if (!command.trim()) return
      const newHistory = [
        command,
        ...commandHistory.filter((cmd) => cmd !== command),
      ].slice(0, 50)
      setCommandHistory(newHistory)
      localStorage.setItem('sshCommandHistory', JSON.stringify(newHistory))
    },
    [commandHistory, setCommandHistory],
  )

  // 滚动条自动滚动
  useEffect(() => {
    // Only scroll if there are logs to display
    if (session.logs.length > 0) {
      // 在 items 更新后滚动到底部
      setTimeout(() => {
        if (editorRef.current) {
          ;(editorRef.current as HTMLElement).scrollTop = (
            editorRef.current as HTMLElement
          ).scrollHeight
        }
      }, 50)
    }
  }, [session.logs])

  useEffect(() => {
    if (session.config.command.trim() === '') {
      setFilteredHistory(commandHistory)
    } else {
      setFilteredHistory(
        commandHistory.filter((cmd) =>
          cmd.toLowerCase().includes(session.config.command.toLowerCase()),
        ),
      )
    }
  }, [session.config.command, commandHistory])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        historyDropdownRef.current &&
        !historyDropdownRef.current.contains(event.target as Node) &&
        commandInputRef.current
      ) {
        const inputElement = commandInputRef.current.input
        if (inputElement && !inputElement.contains(event.target as Node)) {
          setShowCommandHistory(false)
        }
      }
    }
    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // 在滚动的时候执行一些操作
  // const handleScroll = (scrollTop: number, clientHeight: number) => {
  //   console.log('Current scrollTop:', scrollTop)
  //   const editorElement = document.getElementById('editor-body')
  //   if (editorElement) {
  //     console.log(
  //       'editorElement.clientHeight:',
  //       editorElement.clientHeight,
  //       'editor-log clientHeight:',
  //       clientHeight,
  //     )
  //     const start_pos = Math.floor(
  //       (scrollTop / editorElement.clientHeight) * filteredLogs.length,
  //     )
  //     const end_pos = Math.ceil(
  //       ((scrollTop + clientHeight) / editorElement.clientHeight) *
  //         filteredLogs.length,
  //     )

  //     console.log('start_pos:', start_pos, 'end_pos:', end_pos)
  //     setVisibleLogs(filteredLogs.slice(start_pos, end_pos))
  //   }
  // }

  // useScrollListener('editor-log', handleScroll)

  // 连接/断开
  const handleConnect = () => {
    message.loading('正在连接...', 0)
    ConnectSSH(
      session.id,
      session.config.username,
      session.config.password,
      session.config.host,
      session.config.port,
      session.store,
    )
      .then(() => {
        setSession({ isConnected: true })
        message.destroy()
        message.success('服务已建立')
      })
      .catch(() => {
        message.destroy()
        message.error('连接失败')
      })
  }
  const handleDisconnect = () => {
    if (session.isStreaming) {
      message.warning('正在流式传输中, 请先终止流式传输')
      return
    }
    DisconnectSSH(session.id)
      .then(() => {
        setSession({
          isConnected: false,
          isStreaming: false,
          logs: [],
          error: null,
        })
        message.success('服务已断开')
      })
      .catch(() => {
        message.error('断开连接失败')
      })
  }

  // 命令执行 - 使用 useCallback 避免不必要的重新创建
  const handleSubmit = useCallback(() => {
    if (!session.isConnected) {
      message.error('请先建立连接')
      return
    }

    console.log(`[${session.id}] 开始执行命令，设置 isStreaming=true`, session)
    setSession({ logs: [], error: null, isStreaming: true })

    const _start_log_stream = (command: string, stream: boolean) => {
      console.log(
        `[${session.id}] 调用 StartLogStream, command=${command}, stream=${stream}`,
      )
      StartLogStream(session.id, command, stream)
        .then(() => {
          if (stream) {
            console.log(`[${session.id}] 流式传输已启动`)
            message.success('流式传输已启动')
          }
        })
        .catch(() => {
          console.error(
            `[${session.id}] ${stream ? '流式传输启动失败' : '命令执行失败'}`,
          )
          message.error(stream ? '流式传输启动失败' : '命令执行失败')
          // 如果启动失败，重置 isStreaming 状态
          setSession({ isStreaming: false })
        })
    }

    saveCommandToHistory(session.config.command)
    setShowCommandHistory(false)

    CheckCmdIsContinuous(session.config.command)
      .then((res: boolean) => {
        console.log(`[${session.id}] CheckCmdIsContinuous 结果:`, res)
        if (res === true) {
          console.log(`[${session.id}] 命令是连续的，启用流模式`)
          setSession({
            enableStream: true,
            config: { ...session.config, stream: true },
          })
        }
        _start_log_stream(session.config.command, session.config.stream)
      })
      .catch((error) => {
        console.error(`[${session.id}] CheckCmdIsContinuous 错误:`, error)
        _start_log_stream(session.config.command, session.config.stream)
      })
  }, [
    session.id,
    session.isConnected,
    session.config.command,
    session.config.stream,
    setSession,
    saveCommandToHistory,
  ])
  const handleStop = () => {
    console.log(
      `[${session.id}] 停止流式传输，当前 isStreaming=${session.isStreaming}`,
    )

    if (!session.isStreaming) {
      console.warn(`[${session.id}] 尝试停止非流式传输状态的会话`)
      message.warning('当前没有正在进行的流式传输')
      return
    }

    setSession({ enableStream: false })

    StopLogStream(session.id)
      .then(() => {
        console.log(
          `[${session.id}] 流式传输已成功停止，设置 isStreaming=false`,
        )
        message.success('流式传输已停止')
        setSession({
          isStreaming: false,
          config: { ...session.config, stream: false },
        })
      })
      .catch((error) => {
        console.error(`[${session.id}] 流式传输停止失败:`, error)
        message.error('流式传输停止失败')
        // 即使停止失败，也重置状态，避免按钮卡在启用状态
        setSession({ isStreaming: false })
      })
  }

  // 日志过滤和高亮
  useEffect(() => {
    if (session.logs.length > 0) {
      init().then(() => {
        const rules = session.selectedRule.map((rule) => rule.valueOf())
        let filtered_logs: string[] = []
        if (session.filtered) {
          const searchTerms = session.searchTerm.split(',')
          filtered_logs = filter_logs(
            session.logs,
            searchTerms,
            new Int32Array(rules),
          )
        } else {
          filtered_logs = session.logs
        }
        setFilteredLogs(filtered_logs)
      })
    } else {
      setFilteredLogs([])
    }
  }, [session.logs, session.searchTerm, session.selectedRule, session.filtered])
  useEffect(() => {
    const searchTerms = splitStringAndHandle(
      session.searchTerm,
      ',',
      session.selectedRule,
    )
    highlightWithSearch(filteredLogs.join('\n'), searchTerms).then(
      setHighlightedCode,
    )
  }, [filteredLogs, session.searchTerm, session.selectedRule])
  const highlightWithSearch = (code: string, searchTerms: string[]) => {
    return new Promise<string>((resolve) => {
      setTimeout(() => {
        let highlightedCode_ = highlight(code, languages.log, 'log')
        if (
          searchTerms.length === 0 ||
          (searchTerms.length === 1 && searchTerms[0].trim() === '')
        ) {
          resolve(highlightedCode_)
          return
        }
        const colors = [
          'yellow',
          '#FFCCCB',
          '#CCFFCC',
          '#CCCCFF',
          '#FFFFCC',
          '#FFCCFF',
          '#CCFFFF',
        ]
        searchTerms.forEach((searchTerm_, index) => {
          if (searchTerm_.trim() === '') return
          const color = colors[index % colors.length]
          const escapedSearchTerm = escapeRegExp(searchTerm_)
          const regex = new RegExp(`(${escapedSearchTerm})`, 'gi')
          highlightedCode_ = highlightedCode_.replace(
            regex,
            (match) =>
              `<span style="background-color: ${color}; font-weight: bold;">${match}</span>`,
          )
        })
        resolve(highlightedCode_)
      }, 0)
    })
  }

  // 其它辅助方法
  const onSearch: SearchProps['onSearch'] = (value, _e) => {
    const value_ = new HandleStrings(value, session.selectedRule).handle()
    setSession({ searchTerm: value_ })
  }
  const handleOptionChange = (value: number[]) =>
    setSession({ selectedRule: value })
  const openNotification = () => {
    api.info({
      duration: 3,
      message: '填充历史数据',
      description: `${JSON.stringify(session.config)}`,
      style: { textAlign: 'left' },
      onClick: () => {},
      placement: 'bottomRight',
    })
  }
  const autofillSSHConfig = async (host: string, port: number) => {
    if (host && port) {
      const store_data = await GetHostInfo(host, port)
      if (Object.keys(store_data).length > 0) {
        openNotification()
        setSession({
          config: {
            ...session.config,
            host,
            username: store_data.username,
            port,
            password: store_data.password,
          },
        })
      }
    }
  }

  const deleteHostRecord = async (host: string, port: number) => {
    try {
      await DeleteSSHHost(host, port)
      message.success(`已删除主机记录 ${host}:${port}`)
      // 只有当前 session 是活动的时才加载主机列表
      if (session.id === activeSessionId) {
        getHostportListRef.current()
      }
    } catch (error) {
      console.error('删除主机记录失败:', error)
      message.error('删除主机记录失败')
    }
  }

  // 使用 useCallback 确保 handleKeyDown 函数引用稳定
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // 首先检查这是否是活跃的会话，如果不是则直接返回
      // 这样可以确保只有活跃会话才会处理键盘事件
      if (session.id !== activeSessionId) {
        return // 如果不是活跃会话，直接跳过
      }

      switch (event.key.toLowerCase()) {
        case 'f':
          if (event.ctrlKey && searchRef.current) {
            message.open({
              content: '🤔请在文本检索框输入搜索内容',
              key: 'search',
            })
            ;(searchRef.current as HTMLElement).focus()
          }
          break
        case 'enter':
          // Only proceed if Ctrl key is pressed (we already checked this is the active session)
          if (event.ctrlKey) {
            console.log(
              'Ctrl+Enter pressed, session state:',
              session,
              'activeSessionId:',
              activeSessionId,
            )

            // Check if command can be executed
            const canExecute =
              session.isConnected &&
              session.config.command.trim() &&
              !(session.isStreaming && session.config.stream)

            if (canExecute) {
              // Prevent default behavior
              event.preventDefault()

              // Show message and execute command directly
              message.open({ content: '💻开始执行命令', key: 'execute' })

              // Call handleSubmit directly instead of simulating button click
              handleSubmit()
            } else {
              // Provide feedback about why command can't be executed
              if (!session.isConnected) {
                message.warning('请先建立连接')
              } else if (!session.config.command.trim()) {
                message.warning('请输入命令')
              } else if (session.isStreaming && session.config.stream) {
                message.warning('正在流式传输中, 请先终止流式传输')
              }
            }
          }
          break
        default:
          break
      }
    },
    [
      session.isConnected,
      session.isStreaming,
      session.config.command,
      session.config.stream,
      session.id,
      activeSessionId,
      handleSubmit,
      searchRef,
    ],
  )

  // 键盘事件
  useEffect(() => {
    // 直接使用 memoized handleKeyDown 函数，不需要包装
    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleKeyDown])

  // 渲染
  return (
    <>
      {contextHolder}
      <Content
        className="body-area"
        style={{
          display: 'flex',
          flexDirection: 'column',
          height: '100%',
          overflow: 'hidden',
        }}
      >
        <div className="input_form">
          <Space
            id="ssh_form"
            direction="vertical"
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              flexDirection: 'row',
              columnGap: '8px',
              padding: '5px 0',
              marginLeft: '28px',
            }}
          >
            <span className="input_area" id="host">
              <label htmlFor="">主机:</label>
              <Popover
                title={
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <span>主机列表</span>
                  </div>
                }
                trigger="click"
                content={
                  <div>
                    <div
                      style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        padding: '0 4px 8px 4px',
                        borderBottom: '1px solid #f0f0f0',
                        marginBottom: '8px',
                      }}
                    >
                      <span style={{ fontSize: '12px', color: '#8c8c8c' }}>
                        共 {session.hostportList.length} 条记录
                      </span>
                      <CheckHostsAliveButton
                        hostportList={session.hostportList}
                        onStatusChange={(status) =>
                          setSession({ aliveStatus: status })
                        }
                      />
                    </div>
                    <div
                      className="custom-scrollbar"
                      style={{
                        maxHeight: '300px',
                        overflowY: 'auto',
                        width: '200px',
                        msOverflowStyle: 'none',
                        scrollbarWidth: 'none',
                      }}
                    >
                      {session.hostportList.map((hostport, index) => (
                        <div
                          className="host-list-item"
                          key={index}
                          style={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            marginBottom: '8px',
                            padding: '6px 8px',
                            borderRadius: '4px',
                            transition: 'background-color 0.3s',
                            cursor: 'pointer',
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = '#f5f5f5'
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor =
                              'transparent'
                          }}
                        >
                          <div
                            style={{ display: 'flex', alignItems: 'center' }}
                          >
                            <span
                              style={{
                                width: '8px',
                                height: '8px',
                                borderRadius: '50%',
                                backgroundColor: session.aliveStatus[hostport]
                                  ? '#52c41a'
                                  : '#ff4d4f',
                                marginRight: '8px',
                                display: 'inline-block',
                              }}
                            />
                            <p
                              style={{ cursor: 'pointer', margin: 0 }}
                              onClick={async (e) => {
                                e.stopPropagation()
                                const [host, port] = hostport.split(':')
                                await autofillSSHConfig(host, Number(port))
                              }}
                            >
                              {hostport}
                            </p>
                          </div>
                          <Button
                            type="text"
                            icon={<DeleteOutlined />}
                            className="delete-host-btn"
                            style={{
                              backgroundColor: '#f5f5f5',
                              borderRadius: '50%',
                              width: '24px',
                              height: '24px',
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center',
                              border: 'none',
                              opacity: 0.8,
                              color: '#8c8c8c',
                              transition: 'all 0.3s',
                            }}
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation()
                              const [host, port] = hostport.split(':')
                              Modal.confirm({
                                title: '确认删除',
                                content: `确定要删除主机记录 ${hostport} 吗？`,
                                okText: '确认',
                                cancelText: '取消',
                                onOk: () =>
                                  deleteHostRecord(host, Number(port)),
                              })
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                }
              >
                <Input
                  name="host"
                  placeholder="Host"
                  value={session.config.host}
                  disabled={session.isConnected}
                  onClick={() => {
                    // 只有当前 session 是活动的时才加载主机列表
                    if (session.id === activeSessionId) {
                      getHostportListRef.current()
                    }
                  }}
                  onChange={(e) =>
                    setSession({
                      config: { ...session.config, host: e.target.value },
                    })
                  }
                  autoComplete="host"
                />
              </Popover>
            </span>
            <span className="input_area" id="port">
              <label htmlFor="">端口:</label>
              <Input
                placeholder="Port"
                type="number"
                name="port"
                autoComplete="port"
                value={session.config.port}
                disabled={session.isConnected}
                onChange={(e) =>
                  setSession({
                    config: {
                      ...session.config,
                      port: parseInt(e.target.value),
                    },
                  })
                }
              />
            </span>
            <span className="input_area" id="username">
              <label htmlFor="">用户名:</label>
              <Input
                placeholder="Username"
                name="username"
                autoComplete="username"
                value={session.config.username}
                disabled={session.isConnected}
                onChange={(e) =>
                  setSession({
                    config: { ...session.config, username: e.target.value },
                  })
                }
              />
            </span>
            <span className="input_area" id="password">
              <label htmlFor="">密码:</label>
              <Input
                placeholder="Password"
                name="password"
                autoComplete="password"
                type="password"
                value={session.config.password}
                disabled={session.isConnected}
                onChange={(e) =>
                  setSession({
                    config: { ...session.config, password: e.target.value },
                  })
                }
                onClick={async () => {
                  await autofillSSHConfig(
                    session.config.host,
                    session.config.port,
                  )
                }}
              />
            </span>
            <span className="input_area" id="store">
              <label htmlFor="">存储:</label>
              <Switch
                checkedChildren="是"
                unCheckedChildren="否"
                disabled={session.isConnected}
                defaultChecked={false}
                checked={session.store}
                onClick={(checked) => setSession({ store: checked })}
              />
            </span>
            {session.isConnected ? (
              <Button type="primary" onClick={handleDisconnect} danger>
                断开
              </Button>
            ) : (
              <Button type="primary" onClick={handleConnect}>
                连接
              </Button>
            )}
          </Space>
          <div
            id="command"
            style={{
              marginBottom: '5px',
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
            }}
          >
            <label htmlFor="">shell命令:</label>
            <div className="command">
              <div className="command-input-container">
                <Input
                  ref={commandInputRef}
                  placeholder="Command"
                  value={session.config.command}
                  onChange={(e) => {
                    setSession({
                      config: { ...session.config, command: e.target.value },
                    })
                    setShowCommandHistory(true)
                  }}
                  onFocus={() => setShowCommandHistory(true)}
                  onClick={() => setShowCommandHistory(true)}
                  allowClear
                />
                {showCommandHistory && (
                  <div
                    className="command-history-dropdown"
                    ref={historyDropdownRef}
                  >
                    {filteredHistory.length > 0 ? (
                      filteredHistory.map((cmd, index) => (
                        <div
                          key={index}
                          className="command-history-item"
                          onClick={() => {
                            setSession({
                              config: { ...session.config, command: cmd },
                            })
                            setShowCommandHistory(false)
                          }}
                        >
                          {cmd}
                        </div>
                      ))
                    ) : (
                      <div className="command-history-item command-history-empty">
                        暂无历史命令
                      </div>
                    )}
                  </div>
                )}
              </div>
              <div className="switch">
                <label htmlFor="stream">流:</label>
                <Switch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  defaultChecked={false}
                  checked={session.enableStream}
                  onChange={(checked) => setSession({ enableStream: checked })}
                  onClick={(checked) => {
                    if (session.isStreaming) {
                      message.warning('流已开启，请先停止流')
                      return
                    }
                    setSession({
                      config: { ...session.config, stream: checked },
                    })
                  }}
                />
              </div>
              <Button
                ref={executeBtnRef}
                type="primary"
                onClick={handleSubmit}
                disabled={session.isStreaming && session.config.stream}
                style={{ margin: '0 8px 0 0' }}
              >
                执行
              </Button>
              <Button
                type="default"
                onClick={handleStop}
                disabled={!session.isStreaming}
                style={{ margin: '0 0 0 8px' }}
              >
                中止 {session.isStreaming ? '✓' : '✗'}
              </Button>
            </div>
          </div>
          {session.error && (
            <Alert
              message={session.error}
              type="error"
              showIcon
              style={{ marginBottom: '10px', padding: '5px 10px' }}
              closable
            />
          )}
          <div
            className="input_area"
            style={{
              margin: '2px 0',
              justifyContent: 'space-between',
              flexWrap: 'wrap',
              gap: '3px',
            }}
          >
            <div
              className="search-controls"
              style={{
                display: 'flex',
                alignItems: 'center',
                flexWrap: 'wrap',
                flex: '1',
              }}
            >
              <label htmlFor="">文本检索:</label>
              <SearchComponent
                onSearch={onSearch}
                setSearchTerm={(v) => setSession({ searchTerm: v })}
                searchRef={searchRef}
              />
              <span className="filter_area" id="filter">
                <label htmlFor="">过滤:</label>
                <Switch
                  checkedChildren="是"
                  unCheckedChildren="否"
                  defaultChecked={session.filtered}
                  checked={session.filtered}
                  onClick={(checked) => setSession({ filtered: checked })}
                />
              </span>
              <Select
                style={{ width: '170px', marginRight: '9px' }}
                mode="multiple"
                placeholder="字符串处理规则"
                onChange={handleOptionChange}
                options={options}
                maxTagCount="responsive"
                allowClear
              />
              <Divider
                type="vertical"
                style={{
                  borderColor: '#7cb305',
                  marginRight: '15px',
                  height: '22px',
                }}
              />
              <Button type="default" onClick={() => setSession({ logs: [] })}>
                清空日志
              </Button>
            </div>
            {session.searchTerm && (
              <div
                className="search-result-container"
                style={{ display: 'flex', alignItems: 'center' }}
              >
                <label htmlFor="">结果:</label>
                <span className="search-result-count">
                  找到 <strong>{filteredLogs.length}</strong> 条结果
                </span>
              </div>
            )}
          </div>
        </div>
        <div id="editor-log" ref={editorRef}>
          <Editor
            id="editor-body"
            value={filteredLogs.join('\n')}
            onValueChange={() => {}}
            highlight={() => highlightedCode}
            padding={10}
            style={{
              fontFamily: '"Fira code", "Fira Mono", monospace',
              fontSize: 13,
              background: '#f9f9f9',
              border: '1px solid #ccc',
              minHeight: '100%',
              overflowY: 'scroll',
              marginRight: '-10px',
              overflowX: 'hidden',
              whiteSpace: 'pre-wrap',
            }}
          />
        </div>
      </Content>
    </>
  )
}

export default SSHComponent
