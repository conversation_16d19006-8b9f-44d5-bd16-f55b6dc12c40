.http-client-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f0f2f5;
    color: rgba(0, 0, 0, 0.85);
    padding: 16px;
    overflow-y: auto; /* 允许垂直滚动 */
    overflow-x: hidden; /* 隐藏水平滚动 */

    /* HTTP Method Colors */
    .http-method {
        font-weight: 500;

        &.get {
            color: #2db7f5; /* Blue */
        }

        &.post {
            color: #ff9800; /* Orange */
        }

        &.put {
            color: #4caf50; /* Green */
        }

        &.delete {
            color: #f44336; /* Red */
        }

        &.options {
            color: #1890ff; /* Light Blue */
        }

        &.head {
            color: #722ed1; /* Purple */
        }

        &.patch {
            color: #eb2f96; /* Pink */
        }

        &.trace {
            color: #7265e6; /* Indigo */
        }
    }

    .request-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        gap: 8px;

        .method-select {
            width: 110px;
            border-radius: 4px;

            .ant-select-selector {
                padding: 0 8px !important;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .ant-select-selection-item {
                padding: 0 !important;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            /* Style for the selected method in the dropdown */
            .ant-select-selection-item {
                span.http-method {
                    font-weight: 600;
                }
            }
        }

        /* Style for the dropdown items */
        .ant-select-dropdown {
            .ant-select-item {
                padding: 5px 12px;
                text-align: center;

                &.ant-select-item-option-selected {
                    background-color: #f5f5f5 !important;
                }

                .ant-select-item-option-content {
                    text-align: center;
                    justify-content: center;

                    span.http-method {
                        display: block;
                        text-align: center;
                        font-weight: 500;
                    }
                }
            }
        }

        .url-input {
            flex: 1;
            border-radius: 4px;

            &::placeholder {
                color: rgba(0, 0, 0, 0.45);
            }
        }

        .send-button {
            padding: 8px 16px;
            border-radius: 4px;
            background-color: #1890ff;
            color: white;
            border: none;
            cursor: pointer;
            transition: background-color 0.3s;

            &:hover {
                background-color: #40a9ff;
            }

            &:disabled {
                background-color: #f5f5f5;
                color: rgba(0, 0, 0, 0.25);
                cursor: not-allowed;
            }
        }
    }

    .request-response-container {
        display: flex;
        flex: 1;
        gap: 16px;
        overflow-y: auto; /* 允许垂直滚动 */
        overflow-x: hidden; /* 隐藏水平滚动 */
        height: calc(100% - 56px);

        .request-panel,
        .response-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background-color: #ffffff;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .panel-header {
            padding: 10px 12px;
            background-color: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 8px;

            /* 在小屏幕上调整为垂直布局 */
            @media (max-width: 768px) {
                flex-direction: column;
                align-items: flex-start;
            }

            .response-status-info {
                display: flex;
                gap: 16px;
                align-items: center;
                flex-wrap: wrap;

                .status-item, .time-item, .size-item, .waiting-item, .loading-item {
                    display: flex;
                    align-items: center;
                    white-space: nowrap;
                }

                .info-label {
                    font-size: 13px;
                    font-weight: 400;
                    color: rgba(0, 0, 0, 0.65);
                    margin-right: 4px;
                }

                .info-value {
                    font-size: 14px;
                    font-weight: 500;
                    color: rgba(0, 0, 0, 0.85);

                    &.success {
                        color: #52c41a;
                    }

                    &.error {
                        color: #ff4d4f;
                    }
                }

                .loading-item {
                    color: #1890ff;

                    .info-value {
                        color: #1890ff;
                    }
                }

                .waiting-item {
                    color: rgba(0, 0, 0, 0.45);

                    .info-value {
                        color: rgba(0, 0, 0, 0.45);
                    }
                }
            }
        }

        .panel-content {
            flex: 1;
            overflow-y: auto; /* 允许内容溢出，不显示滚动条 */
            padding: 12px;
            display: flex;
            flex-direction: column;
            height: auto; /* 自动调整高度 */
            scrollbar-width: none; /* Firefox 隐藏滚动条 */
            -ms-overflow-style: none; /* IE / Edge 隐藏滚动条 */

            &::-webkit-scrollbar {
                display: none;
            }
        }
    }

    .ant-tabs {
        color: rgba(0, 0, 0, 0.85);
        display: flex;
        flex-direction: column;
        height: 100%;

        .ant-tabs-nav {
            margin-bottom: 8px;

            &::before {
                border-bottom: 1px solid #f0f0f0;
            }
        }

        .ant-tabs-tab {
            color: rgba(0, 0, 0, 0.65);

            &.ant-tabs-tab-active .ant-tabs-tab-btn {
                color: #1890ff;
            }
        }

        .ant-tabs-ink-bar {
            background: #1890ff;
        }

        .ant-tabs-content-holder {
            flex: 1;
            overflow: visible; /* 允许内容溢出，不显示滚动条 */
        }

        .ant-tabs-content {
            height: 100%; /* 自动调整高度 */
        }

        .ant-tabs-tabpane {
            height: 100%; /* 自动调整高度 */
            overflow: visible; /* 允许内容溢出，不显示滚动条 */
        }
    }

    .params-table {
        width: 100%;

        .ant-table {
            background-color: transparent;

            .ant-table-thead > tr > th {
                background-color: #fafafa;
                border-bottom: 1px solid #f0f0f0;
            }

            .ant-table-tbody > tr > td {
                border-bottom: 1px solid #f0f0f0;
            }

            .ant-table-tbody > tr:hover > td {
                background-color: rgba(0, 0, 0, 0.02);
            }
        }

        .ant-input {
            border: 1px solid #d9d9d9;

            &::placeholder {
                color: rgba(0, 0, 0, 0.45);
            }
        }

        .ant-btn {
            background-color: transparent;
            border-color: #d9d9d9;

            &:hover {
                color: #1890ff;
                border-color: #1890ff;
            }
        }
    }

    .response-status {
        display: flex;
        gap: 16px;
        margin-bottom: 8px;

        .status-item {
            display: flex;
            align-items: center;
            gap: 4px;

            .label {
                color: rgba(0, 0, 0, 0.65);
            }

            .value {
                font-weight: 500;
            }

            &.success .value {
                color: #52c41a;
            }

            &.error .value {
                color: #ff4d4f;
            }
        }
    }

    .response-body {
        background-color: #fafafa;
        border-radius: 4px;
        padding: 12px;
        font-family: "Consolas", "Monaco", "Andale Mono", monospace;
        overflow: auto;
        white-space: pre-wrap;
        word-break: break-all;
        border: 1px solid #f0f0f0;
    }

    .response-headers {
        .ant-table {
            margin-top: 8px;

            .ant-table-thead > tr > th {
                background-color: #fafafa;
                font-weight: 500;
            }
        }
    }

    .test-results {
        .test-summary {
            display: flex;
            gap: 24px;
            margin-bottom: 16px;
            padding: 12px;
            background-color: #fafafa;
            border-radius: 4px;

            .test-summary-item {
                display: flex;
                align-items: center;
                gap: 8px;
            }
        }

        .ant-table {
            .ant-table-thead > tr > th {
                background-color: #fafafa;
                font-weight: 500;
            }
        }
    }

    .response-tabs {
        height: 100%; /* 自动调整高度 */
        display: flex;
        flex-direction: column;

        .ant-tabs-content {
            height: 100%; /* 自动调整高度 */
            flex: 1;
            overflow: visible; /* 允许内容溢出 */
            min-height: 300px; /* 确保有足够的最小高度 */
        }

        .ant-tabs-tabpane {
            height: 100%; /* 自动调整高度 */
            overflow: visible; /* 允许内容溢出 */
            display: flex;
            flex-direction: column;
            min-height: 300px; /* 确保有足够的最小高度 */
        }

        /* 确保响应内容正确换行 */
        .ant-tabs-tabpane-active {
            overflow: visible;
            white-space: pre-wrap;
            word-break: break-word;
        }
    }

    /* 确保编辑器容器占满高度并完全隐藏所有滚动条 */
    .response-body-container {
        display: flex;
        flex-direction: column;
        height: 100%;
        min-height: 300px; /* 确保有足够的最小高度 */

        .response-render-controls {
            display: flex;
            margin-bottom: 12px;
            align-items: center;
            gap: 12px;
            background-color: #fafafa;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #f0f0f0;

            .control-group {
                display: flex;
                align-items: center;

                .control-label {
                    font-size: 10px;
                    color: rgba(0, 0, 0, 0.65);
                    margin-right: 6px;
                }

                .ant-select {
                    font-size: 10px;

                    .ant-select-selector {
                        padding: 0 6px;
                        height: 20px !important;

                        .ant-select-selection-item {
                            line-height: 18px;
                        }
                    }

                    // 调整下拉菜单的样式
                    &.ant-select-open .ant-select-dropdown {
                        font-size: 10px;

                        .ant-select-item {
                            padding: 2px 6px;
                            min-height: 20px;
                            line-height: 16px;
                        }
                    }
                }

                .render-mode-select {
                    width: 80px;
                }

                .language-select {
                    width: 80px;
                }

                .ant-select-selector {
                    font-size: 10px;
                }
            }
        }

        .html-preview-container {
            flex: 1;
            border: 1px solid #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            background-color: #fff;
            height: calc(100% - 40px);
            min-height: 200px;

            .html-preview-frame {
                width: 100%;
                height: 100%;
                border: none;
            }

            .preview-not-available {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 100%;
                background-color: #fafafa;
            }
        }
    }

    .monaco-editor-container {
        flex: 1;
        min-height: 300px;
        position: relative;
        border: 1px solid #f0f0f0; /* 添加边框以明确容器边界 */
        border-radius: 4px;
        padding: 0; /* 确保内容正确显示 */
    }

    /* 认证相关样式 */
    .auth-container {
        padding: 24px;
        background-color: #fafafa;
        border-radius: 6px;
        border: 1px solid #f0f0f0;
        max-width: 600px;
        margin: 0 auto;
    }

    .auth-type-selector {
        margin-bottom: 24px;
    }

    .auth-label {
        margin-bottom: 8px;
        font-size: 14px;
    }

    .auth-form {
        background-color: white;
        padding: 20px;
        border-radius: 6px;
        border: 1px solid #f0f0f0;
        margin-bottom: 20px;
    }

    .auth-form-item {
        margin-bottom: 16px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .auth-input-icon {
        margin-right: 8px;
        opacity: 0.7;
    }

    .auth-info {
        display: flex;
        align-items: flex-start;
        padding: 12px 16px;
        background-color: #e6f7ff;
        border: 1px solid #91d5ff;
        border-radius: 4px;
        margin-top: 16px;
    }

    .auth-info-icon {
        margin-right: 8px;
        font-size: 16px;
    }

    .auth-info-text {
        flex: 1;
        font-size: 13px;
        line-height: 1.5;
    }
}
