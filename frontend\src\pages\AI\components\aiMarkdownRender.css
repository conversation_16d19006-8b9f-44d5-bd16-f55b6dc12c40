.ai-markdown-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  padding: 16px;
  max-width: 100%;
  overflow-x: auto;
}

/* Headings */
.markdown-heading {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

.markdown-h1 {
  font-size: 2em;
}

.markdown-h2 {
  font-size: 1.5em;
}

.markdown-h3 {
  font-size: 1.25em;
}

.markdown-h4 {
  font-size: 1em;
}

.markdown-h5 {
  font-size: 0.875em;
}

.markdown-h6 {
  font-size: 0.85em;
  color: #6a737d;
}

/* Paragraphs */
.markdown-paragraph {
  margin-top: 0;
  margin-bottom: 16px;
}

/* Links */
.markdown-link {
  color: #0366d6;
  text-decoration: none;
}

.markdown-link:hover {
  text-decoration: underline;
}

/* Lists */
.markdown-list {
  padding-left: 2em;
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-list-item {
  margin-bottom: 4px;
}

/* Code blocks */
.code-block-container {
  margin: 16px 0;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.code-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #2d2d2d;
  padding: 8px 16px;
  color: #e6e6e6;
  font-family: monospace;
  font-size: 0.85em;
}

.code-block-filepath {
  background-color: #1e1e1e;
  padding: 6px 16px;
  color: #e6e6e6;
  font-family: monospace;
  font-size: 0.85em;
  border-bottom: 1px solid #444;
}

.code-language-label {
  text-transform: uppercase;
  font-size: 0.75em;
  font-weight: bold;
  letter-spacing: 0.5px;
}

.copy-button {
  background: transparent;
  border: none;
  color: #e6e6e6;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.copy-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.inline-code {
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 85%;
  margin: 0;
  padding: 0.2em 0.4em;
}

/* Tables */
.table-container {
  overflow-x: auto;
  margin-bottom: 16px;
}

.markdown-table {
  border-collapse: collapse;
  width: 100%;
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-table th,
.markdown-table td {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.markdown-table th {
  background-color: #f6f8fa;
  font-weight: 600;
}

.markdown-table tr:nth-child(2n) {
  background-color: #f6f8fa;
}

/* Blockquotes */
.markdown-blockquote {
  margin: 0 0 16px;
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
}

.markdown-blockquote > :first-child {
  margin-top: 0;
}

.markdown-blockquote > :last-child {
  margin-bottom: 0;
}

/* Images */
.markdown-image {
  max-width: 100%;
  box-sizing: border-box;
  margin: 16px 0;
  border-radius: 4px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .ai-markdown-container {
    color: #e6e6e6;
    background-color: #1e1e1e;
  }

  .markdown-heading {
    border-bottom-color: #444;
  }

  .markdown-link {
    color: #58a6ff;
  }

  .inline-code {
    background-color: rgba(240, 246, 252, 0.15);
    color: #e6e6e6;
  }

  .markdown-table th,
  .markdown-table td {
    border-color: #444;
  }

  .markdown-table th {
    background-color: #2d2d2d;
  }

  .markdown-table tr:nth-child(2n) {
    background-color: #2d2d2d;
  }

  .markdown-blockquote {
    color: #8b949e;
    border-left-color: #444;
  }
}