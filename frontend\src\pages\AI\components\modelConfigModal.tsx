import React, { useCallback, useEffect, useState } from 'react'
import { Modal, Form, Select, Input, Button, Space, message } from 'antd'
import { CheckCircleOutlined, LoadingOutlined } from '@ant-design/icons'
import { ModelConfig } from '../../../types'
import {
  CheckAIConnect,
  GetProviderModels,
} from '../../../../wailsjs/go/main/AiApp'
import { AiEventsEnum } from '../../../events'
import { EventsOff, EventsOn } from '../../../../wailsjs/runtime/runtime'

const { Option } = Select

interface ModelConfigModalProps {
  visible: boolean
  onCancel: () => void
  onSave: (config: ModelConfig) => void
  initialConfig?: ModelConfig
}

const ModelConfigModal: React.FC<ModelConfigModalProps> = ({
  visible,
  onCancel,
  onSave,
  initialConfig,
}) => {
  const [form] = Form.useForm()
  const [testing, setTesting] = useState(false)
  const [testResult, setTestResult] = useState<'success' | 'error' | null>(null)
  const [selectedProvider, setSelectedProvider] = useState<
    'ollama' | 'openai' | undefined
  >(initialConfig?.provider)

  const [models, setModels] = useState<string[]>([])

  const handleProviderChange = (value: 'ollama' | 'openai') => {
    setSelectedProvider(value)
    // 根据供应商设置默认API URL
    const defaultUrls = {
      ollama: 'http://192.100.8.27:11434',
      openai: 'https://dashscope.aliyuncs.com/compatible-mode/v1',
    }
    form.setFieldsValue({ apiUrl: defaultUrls[value] })
    // 清除之前的测试结果
    setTestResult(null)
    setModels([]) // 重置模型列表
    // 清空model字段
    form.setFieldsValue({ model: undefined })
  }

  const handleTestConnection = async () => {
    try {
      // 根据供应商类型决定需要验证的字段
      const currentProvider = form.getFieldValue('provider')
      const requiredFields = ['provider', 'apiUrl', 'model']

      // OpenAI 需要 API Key，Ollama 可选
      if (currentProvider === 'openai') {
        requiredFields.push('apiKey')
      }

      setTesting(true)
      setTestResult(null)

      try {
        // 调用后端API进行连接测试
        const success = await getModels()

        if (success.length > 0) {
          setTestResult('success')
          message.success('连接测试成功！')
          setModels(success)
        } else {
          setTestResult('error')
          message.error('连接测试失败，请检查配置信息')
        }
      } catch (error) {
        console.error('Connection test error:', error)
        setTestResult('error')
        message.error('连接测试失败，请检查网络连接和配置信息')
      }
    } catch (error) {
      message.error('请先填写必要的配置信息')
    } finally {
      setTesting(false)
    }
  }

  const handleSave = async () => {
    try {
      const values = await form.validateFields()
      onSave(values)
      message.success('配置保存成功！')
      onCancel()
    } catch (error) {
      message.error('请填写完整的配置信息')
    }
  }

  const handleCancel = () => {
    form.resetFields()
    setTestResult(null)
    setSelectedProvider(undefined)
    onCancel()
  }

  // 当 modal 打开时，设置初始的 provider 状态
  React.useEffect(() => {
    if (visible && initialConfig?.provider) {
      setSelectedProvider(initialConfig.provider)
    }
  }, [visible, initialConfig])

  const errorListener = React.useCallback((payload: any) => {
    console.log('error payload', payload)
    message.error(payload)
  }, [])

  useEffect(() => {
    // 注册事件监听器
    EventsOn(AiEventsEnum.AIChatError, errorListener)
    // 清理监听
    return () => {
      EventsOff(AiEventsEnum.AIChatError)
    }
  }, [errorListener])

  const getModels = async (): Promise<string[]> => {
    if (!selectedProvider) {
      message.warning('请先选择模型供应商')
      return []
    }
    const results = await GetProviderModels(
      selectedProvider,
      form.getFieldValue('apiUrl'),
      form.getFieldValue('apiKey') || '',
    )
    if (!results) {
      message.error('获取模型列表失败')
      return []
    }
    return results
  }

  // 生成option列表
  const options = useCallback(
    () => (
      <>
        {models.map((item) => (
          <Option key={item} value={item}>
            {item}
          </Option>
        ))}
      </>
    ),
    [models],
  )

  return (
    <Modal
      title="模型配置"
      open={visible}
      onCancel={handleCancel}
      width={600}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          保存配置
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={initialConfig}
        style={{ marginTop: 16 }}
      >
        <Form.Item
          label="模型供应商"
          name="provider"
          rules={[{ required: true, message: '请选择模型供应商' }]}
        >
          <Select
            placeholder="请选择模型供应商"
            onChange={handleProviderChange}
            size="large"
          >
            <Option value="ollama">Ollama</Option>
            <Option value="openai">OpenAI</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="API URL"
          name="apiUrl"
          rules={[
            { required: true, message: '请输入API URL' },
            { type: 'url', message: '请输入有效的URL格式' },
          ]}
        >
          <Input placeholder="请输入API URL" size="large" />
        </Form.Item>

        <Form.Item
          label="API Key"
          name="apiKey"
          rules={[
            {
              required: selectedProvider === 'openai',
              message: 'OpenAI 需要提供 API Key',
            },
          ]}
          extra={
            selectedProvider === 'ollama' ? '对于 Ollama, API Key 是可选的' : ''
          }
        >
          <Input.Password
            placeholder={
              selectedProvider === 'ollama' ? 'API Key (可选)' : '请输入API Key'
            }
            size="large"
          />
        </Form.Item>

        <Form.Item style={{ marginBottom: 16, textAlign: 'left' }}>
          <Space>
            <Button
              type="default"
              icon={testing ? <LoadingOutlined /> : <CheckCircleOutlined />}
              onClick={handleTestConnection}
              loading={testing}
              disabled={testing}
            >
              {testing ? '连接测试中...' : '连接验证'}
            </Button>
            {testResult === 'success' && (
              <span style={{ color: '#52c41a' }}>✓ 连接成功</span>
            )}
            {testResult === 'error' && (
              <span style={{ color: '#ff4d4f' }}>✗ 连接失败</span>
            )}
          </Space>
        </Form.Item>

        <Form.Item
          label="模型"
          name="model"
          rules={[{ required: true, message: '请输入模型名称' }]}
        >
          <Select
            placeholder="请选择或输入模型名称"
            size="large"
            showSearch
            allowClear
            filterOption={(input, option) =>
              (option?.children as unknown as string)
                ?.toLowerCase()
                .includes(input.toLowerCase())
            }
          >
            {selectedProvider === 'openai' && options()}
            {selectedProvider === 'ollama' && options()}
          </Select>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default ModelConfigModal
