/**
 * Converts a file size in bytes to a human-readable string with appropriate units (B, KB, MB, GB).
 *
 * @param size - The file size in bytes
 * @returns A formatted string representing the file size with the most appropriate unit
 */
function AdaptSize(size: number): string {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}

export { AdaptSize }
