import QueryRules from '../../eums'
import { HandleStrings } from '../../utils'

/**
 * Splits a string by a delimiter and applies string handling rules to each resulting item.
 *
 * @param input The input string to be split
 * @param delimiter The character or string used to split the input
 * @param h_type An array of query rules to apply to each split item
 * @returns An array of processed search terms after splitting and handling
 */
function splitStringAndHandle(
  input: string,
  delimiter: string,
  h_type: QueryRules[],
) {
  const searchTerms = input
    .split(delimiter)
    .map((item) => new HandleStrings(item, h_type).handle())
  return searchTerms
}

export { splitStringAndHandle }
