import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    rollupOptions: {
      onwarn(warning, warn) {
        // 忽略 vfile 包中的 Node.js 包导入映射警告
        if (
          warning.code === 'UNRESOLVED_IMPORT' &&
          (warning.source === '#minpath' ||
            warning.source === '#minproc' ||
            warning.source === '#minurl')
        ) {
          return
        }
        warn(warning)
      },
    },
  },
  resolve: {
    alias: {
      // 为 Node.js 包导入映射提供别名
      '#minpath': 'path-browserify',
      '#minproc': 'process/browser',
      '#minurl': '/src/polyfills/minurl.js',
    },
  },
})
