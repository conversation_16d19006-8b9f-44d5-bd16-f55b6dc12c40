.notion-theme {
    .ai-markdown-renderer {
        color: #3b3b3b;
        background-color: #fafafa;
        font-family: "Inter", sans-serif;
    }

    .markdown-heading {
        border-bottom-color: #ddd;
        color: #2c2c2c;
    }

    .markdown-h1,
    .markdown-h2,
    .markdown-h3,
    .markdown-h4,
    .markdown-h5,
    .markdown-h6 {
        color: #2c2c2c;
    }

    .markdown-link {
        color: #2f8cff;
        text-decoration: underline;
    }

    .inline-code {
        background-color: #eeeeee;
        color: #3b3b3b;
        border-radius: 4px;
        padding: 0.2em 0.4em;
    }

    .code-block-container {
        background-color: #f3f3f3;
        border: 1px solid #ddd;
    }

    .code-block-header {
        background-color: #eaeaea;
        color: #333;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .copy-button {
        color: #333;
    }

    .markdown-table th,
    .markdown-table td {
        border-color: #ddd;
        background-color: #fff;
    }

    .markdown-blockquote {
        color: #57606a;
        border-left-color: #ccc;
        background-color: #f0f0f0;
    }
}
