.ai-page {
    height: 100%;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
}

/* 设置按钮样式 */
.settings-button-container {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
}

.settings-button {
    color: #666 !important;
    font-size: 18px !important;
    width: 44px !important;
    height: 44px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: white !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
}

.settings-button {
    &:hover {
        color: #1890ff !important;
        transform: rotate(90deg) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    }
}

.initial-input-container {
    width: 100%;
    max-width: 600px;
}

.initial-input-wrapper {
    position: relative;
    background: white;
    border-radius: 24px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 8px;
}

.initial-input {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    resize: none;
    padding: 12px 60px 12px 20px !important;
    font-size: 16px;
    border-radius: 20px !important;
}

.initial-input {
    &:focus {
        border: none !important;
        box-shadow: none !important;
    }
}

/* 聊天状态样式 */
.ai-chat-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    padding-bottom: 10px;
}

.chunk-wrapper {
    margin-bottom: 16px;
    width: 100%;
}

.chunk-wrapper.user-chunk {
    display: flex;
    justify-content: flex-end;
}

.chunk-wrapper.ai-chunk {
    display: flex;
    justify-content: flex-start;
}

.chunk-content {
    display: flex;
    align-items: flex-end;
    max-width: 70%;
    gap: 8px;
}

.chunk-avatar {
    flex-shrink: 0;
    margin-bottom: 4px;
}

.chunk-bubble {
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
    white-space: pre-wrap;
}

.user-chunk .chunk-bubble {
    background: #c1d3e3;
    color: white;
    border-bottom-right-radius: 6px;
}

.ai-chunk .chunk-bubble {
    background: white;
    color: #333;
    border-bottom-left-radius: 6px;
}

.loading-bubble {
    background: #f0f0f0 !important;
    color: #666 !important;
}

/* 底部输入框样式 */
.input-container {
    border-top: 1px solid #e8e8e8;
    padding: 16px 20px;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.input-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    background: white;
    border-radius: 24px;
    padding: 8px;
}

.chat-input {
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    resize: none;
    padding: 12px 60px 12px 20px !important;
    font-size: 16px;
    border-radius: 20px !important;
}

.chat-input {
    &:focus {
        border: none !important;
        box-shadow: none !important;
    }
}

/* 发送按钮样式 */
.send-button {
    position: absolute !important;
    right: 12px;
    bottom: 12px;
    border-radius: 50% !important;
    width: 36px !important;
    height: 36px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 !important;
    border: none !important;
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3) !important;
}

.send-button {
    &:hover {
        transform: scale(1.05);
        transition: transform 0.2s ease;
    }
}

.send-button {
    &:disabled {
        transform: none !important;
        opacity: 0.5;
    }
}

/* 滚动条样式 */
.messages-container {
    &::-webkit-scrollbar {
        width: 6px;
    }
}

.messages-container {
    &::-webkit-scrollbar-track {
        background: transparent;
    }
}

.messages-container {
    &::-webkit-scrollbar-thumb {
        background: #d9d9d9;
        border-radius: 3px;
    }
}

.messages-container {
    &::-webkit-scrollbar-thumb {
        &:hover {
            background: #bfbfbf;
        }
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .messages-container {
        padding: 16px 12px;
    }

    .input-container {
        padding: 12px 16px;
    }

    .chunk-content {
        max-width: 85%;
    }

    .initial-input-container {
        max-width: 90%;
    }
}

/* Ant Design 组件覆盖 */
.ant-input {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.ant-btn-primary {
    background: #1890ff;
    border-color: #1890ff;
}

.ant-btn-primary:hover {
    background: #40a9ff;
    border-color: #40a9ff;
}

.chat-header {
    position: absolute;
    top: 20px;
    left: 20px;
    z-index: 10;
}

/* 初始状态样式 */
.ai-chat-initial {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
}

.ant-space-item {
    width: 100%;
}

.chunk-content .chunk-bubble {
    text-align: left !important;
}
