// HTTP客户端类型定义

export interface HTTPParam {
  id: number
  key: string
  value: string
}

export interface HTTPRequest {
  method: string
  url: string
  headers: { [key: string]: string }
  params: HTTPParam[]
  body: string
}

export interface HTTPResponse {
  status: number
  statusText: string
  headers: { [key: string]: string }
  body: string
  size: number
  time: number
  contentType: string
  error?: string
}

// 创建HTTPRequest的辅助函数
export function createHTTPRequest(
  source: Partial<HTTPRequest> = {},
): HTTPRequest {
  return {
    method: source.method || 'GET',
    url: source.url || '',
    headers: source.headers || {},
    params: source.params || [],
    body: source.body || '',
  }
}

// 将数组转换为HTTPParam数组的辅助函数
export function convertToHTTPParams(
  params: Array<{ id: number; key: string; value: string }>,
): HTTPParam[] {
  return params.map((param) => ({
    id: param.id,
    key: param.key,
    value: param.value,
  }))
}
