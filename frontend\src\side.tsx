import { Layout, Menu, message } from 'antd'
import { useEffect, useState } from 'react'

const { Sider } = Layout

export default function SideBarComponent({
  onChange,
}: {
  onChange: (value: string) => void
}) {
  return (
    <Sider className="menu-area" width={200}>
      <Menu
        className="menu-area"
        mode="inline"
        defaultSelectedKeys={['1']}
        style={{ height: '100%' }}
        onSelect={({ key }) => onChange(key)}
      >
        <Menu.Item id="menu_item" key="1" type="item">
          SSH Viewer
        </Menu.Item>
        <Menu.Item id="menu_item" key="2" type="item">
          Content Share
        </Menu.Item>
        <Menu.Item id="menu_item" key="3" type="item">
          Light HTTP Client
        </Menu.Item>
        <Menu.Item id="menu_item" key="4" type="item">
          AI Chat
        </Menu.Item>
        <Menu.Item
          id="menu_item"
          key="5"
          type="item"
          onClick={() => message.warning('功能开发中...')}
        >
          About
        </Menu.Item>
      </Menu>
    </Sider>
  )
}
