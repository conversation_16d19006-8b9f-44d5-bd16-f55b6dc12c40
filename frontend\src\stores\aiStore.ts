import { create } from 'zustand'
import { schema } from '../../wailsjs/go/models'
import { AIChatMessage } from '../types'
import { ChatWithAIStream, StopChat } from '../../wailsjs/go/main/AiApp'
import { message } from 'antd'

interface StoreState {
  messages: schema.Message[]
  // 是否还在响应中
  responding: boolean

  // 更新用户和ai回答的消息
  appendMessages: (message: schema.Message) => void
  clearMessages: () => void

  // 更新responding
  setResponding: (responding: boolean) => void
}

const useAIStore = create<StoreState>((set) => ({
  messages: [],
  responding: false,
  appendMessages: (message: schema.Message) => {
    set((state) => ({
      messages: [...state.messages, message],
    }))
  },
  clearMessages: () => set({ messages: [] }),
  setResponding: (responding) => set({ responding }),
}))

function setResponding(value: boolean) {
  console.log('setResponding called with:', value)
  useAIStore.getState().setResponding(value)
  console.log(
    'setResponding - current state:',
    useAIStore.getState().responding,
  )
}

// 更新messages. 主要是来自ai的回答
function appendMessages(message: schema.Message) {
  useAIStore.getState().appendMessages(message)
}

// 这里需要监听wails event
async function sendMessage({
  provider,
  content,
  apiUrl,
  apiKey,
  model,
}: Partial<AIChatMessage>) {
  if (!provider || !apiUrl || !model) {
    message.error(
      'Missing required parameters. `provider`, `apiUrl`, and `model` are required.',
    )
    setResponding(false)
    return
  }

  try {
    // 先发送用户的消息
    const userMessage = schema.Message.createFrom({
      role: 'user',
      content,
    })
    appendMessages(userMessage)

    // 调用后端API进行流式聊天
    console.log(`provider: ${provider}, apiUrl: ${apiUrl},  model: ${model}`)
    await ChatWithAIStream(
      provider,
      apiUrl,
      apiKey || '', // ollama可以不需要apiKey
      model,
      useAIStore.getState().messages, // 这里把历史所有数据都发送给大模型
    )
  } catch (error) {
    console.error('Send message error:', error)
    setResponding(false)
    message.error('发送消息失败，请重试')
  }
}

/**
 * Stops the current chat interaction and updates the responding state.
 * Calls the StopChat method to halt the ongoing chat and sets the responding flag to false.
 *
 * @async
 * @returns {Promise<void>} A promise that resolves when the chat is stopped
 */
async function stopChat(): Promise<void> {
  await StopChat()
  setResponding(false)
}

function clearMessages() {
  useAIStore.getState().clearMessages()
}

export {
  useAIStore,
  sendMessage,
  appendMessages,
  stopChat,
  clearMessages,
  setResponding,
}
