import React, { useState, useRef, useEffect } from 'react'
import { Input, Button, Avatar, Space, Typography, Spin, message } from 'antd'
import {
  SendOutlined,
  UserOutlined,
  RobotOutlined,
  SettingOutlined,
} from '@ant-design/icons'
import ReactMarkdown from 'react-markdown'
import ModelConfigModal from './components/modelConfigModal'
import './index.scss'
import {
  appendMessages,
  sendMessage,
  setResponding,
  useAIStore,
} from '../../stores'
import { updateConfig, useSettingsStore } from '../../stores/aiSettings'
import { ModelConfig } from '../../types'
import { EventsOff, EventsOn } from '../../../wailsjs/runtime/runtime'
import { schema } from '../../../wailsjs/go/models'
import { AiEventsEnum } from '../../events'
import AIMarkdownRender from './components/markdown'
import { Markdown } from '../../components/markdown'

const { Text } = Typography

const AIComponent: React.FC = () => {
  const responding = useAIStore((state) => state.responding)
  // 包括历史用户和AI的回答
  const messageAll = useAIStore((state) => state.messages)
  // 当前正在流式传输的AI回答内容
  const [currentStreamingContent, setCurrentStreamingContent] = useState('')
  // 当前正在流式传输的消息ID
  const [currentStreamingId, setCurrentStreamingId] = useState<string | null>(
    null,
  )

  // 使用 ref 来跟踪当前流式内容，避免闭包问题
  const currentStreamingContentRef = useRef('')

  // 调试：监听 responding 状态变化
  useEffect(() => {
    console.log('responding state changed:', responding)
  }, [responding])

  // 同步 ref 和 state
  useEffect(() => {
    currentStreamingContentRef.current = currentStreamingContent
  }, [currentStreamingContent])
  const config = useSettingsStore((state) => state.config)
  const [inputValue, setInputValue] = useState('')
  const [configModalVisible, setConfigModalVisible] = useState(false)
  const modelConfig: ModelConfig = {
    provider: config.provider,
    apiUrl: config.apiUrl,
    apiKey: config.apiKey,
    model: config.model,
  }
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messageAll, currentStreamingContent])

  const streamListener = React.useCallback((payload: schema.Message) => {
    console.log('stream payload', payload)
    if (typeof payload !== 'object') {
      console.warn('Invalid payload received:', payload)
      return
    }

    // 流式传输过程中，只更新当前流式内容，不更新messagesStore
    if (payload.content) {
      setCurrentStreamingContent((prev) => {
        const newContent = prev + payload.content
        return newContent
      })
      setCurrentStreamingId((prev) => {
        if (!prev) {
          // 生成一个临时ID用于流式传输
          return `streaming-${Date.now()}`
        }
        return prev
      })
    }
  }, [])

  const stopListener = React.useCallback((payload: any) => {
    console.log('stop payload', payload)
    message.info(payload)

    // 使用 setTimeout 确保状态更新的顺序
    setTimeout(() => {
      // 清理流式传输状态并设置响应状态
      setCurrentStreamingContent('')
      setCurrentStreamingId(null)
      setResponding(false)
    }, 0)
  }, [])

  const finishListener = React.useCallback((payload: any) => {
    console.log('finish payload', payload)
    message.info(payload)

    // 使用 setTimeout 确保状态更新在下一个事件循环中执行
    setTimeout(() => {
      // 使用 ref 获取最新的流式内容
      const currentContent = currentStreamingContentRef.current
      console.log('finishListener - currentContent from ref:', currentContent)

      // 只有在有流式传输内容时，才将完整的AI回答添加到messagesStore
      if (currentContent.trim()) {
        const aiMessage = schema.Message.createFrom({
          role: 'assistant',
          content: currentContent,
        })
        appendMessages(aiMessage)
      }

      // 清理状态
      setCurrentStreamingContent('')
      setCurrentStreamingId(null)
      setResponding(false)
    }, 0)
  }, [])

  const errorListener = React.useCallback((payload: any) => {
    console.log('error payload', payload)
    message.error(payload)

    // 使用 setTimeout 确保状态更新的顺序
    setTimeout(() => {
      // 清理流式传输状态并设置响应状态
      setCurrentStreamingContent('')
      setCurrentStreamingId(null)
      setResponding(false)
    }, 0)
  }, [])

  useEffect(() => {
    // 注册事件监听器
    EventsOn(AiEventsEnum.AIChatStream, streamListener)
    EventsOn(AiEventsEnum.AIChatStop, stopListener)
    EventsOn(AiEventsEnum.AIChatFinish, finishListener)
    EventsOn(AiEventsEnum.AIChatError, errorListener)
    // 清理监听
    return () => {
      EventsOff(
        AiEventsEnum.AIChatStream,
        AiEventsEnum.AIChatStop,
        AiEventsEnum.AIChatFinish,
        AiEventsEnum.AIChatError,
      )
    }
  }, [streamListener, stopListener, finishListener, errorListener])

  // 执行发送按钮
  const handleSend = async () => {
    if (!inputValue.trim()) return

    // 清理之前的流式传输状态
    setCurrentStreamingContent('')
    setCurrentStreamingId(null)

    // 设置响应状态
    setResponding(true)

    try {
      await sendMessage({
        ...modelConfig,
        content: inputValue.trim(),
      })
      setInputValue('')
    } catch (error) {
      console.error('Send message error:', error)
      setResponding(false)
      message.error('发送消息失败，请重试')
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSend()
    }
  }

  const handleConfigSave = (config: ModelConfig) => {
    updateConfig(config)
    // 这里可以将配置保存到localStorage或发送到后端
    localStorage.setItem('aiModelConfig', JSON.stringify(config))
  }

  // 页面加载时从localStorage读取配置
  useEffect(() => {
    const savedConfig = localStorage.getItem('aiModelConfig')
    if (savedConfig) {
      try {
        updateConfig(JSON.parse(savedConfig))
      } catch (error) {
        console.error('Failed to parse saved config:', error)
      }
    }
  }, [])

  const renderInitialState = () => (
    <div className="ai-chat-initial">
      <div className="settings-button-container">
        <Button
          type="text"
          icon={<SettingOutlined />}
          onClick={() => setConfigModalVisible(true)}
          className="settings-button"
          size="large"
        />
      </div>
      <div className="initial-input-container">
        <div className="initial-input-wrapper">
          <Input.TextArea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="请输入您的问题..."
            className="initial-input"
            autoSize={{ minRows: 1, maxRows: 4 }}
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSend}
            disabled={!inputValue.trim()}
            className="send-button"
          />
        </div>
      </div>
    </div>
  )

  const renderChatState = () => (
    <div className="ai-chat-container">
      <div className="chat-header">
        <Button
          type="text"
          icon={<SettingOutlined />}
          onClick={() => setConfigModalVisible(true)}
          className="settings-button"
          size="large"
        />
      </div>
      <div className="messages-container">
        {/* 显示历史消息 */}
        {messageAll.map((msg, index) => (
          <div
            key={`${msg.role}-${index}`}
            className={`chunk-wrapper ${msg.role === 'user' ? 'user-chunk' : 'ai-chunk'}`}
          >
            <div className="chunk-content">
              {msg.role === 'assistant' && (
                <Avatar
                  icon={<RobotOutlined />}
                  className="chunk-avatar ai-avatar"
                  style={{ backgroundColor: '#1890ff' }}
                />
              )}
              <div className="chunk-bubble">
                {msg.role === 'assistant' ? (
                  // <Markdown enableCopy={true} animated={true}>
                  //   {msg.content}
                  // </Markdown>
                  <AIMarkdownRender content={msg.content}></AIMarkdownRender>
                ) : (
                  <Text>{msg.content}</Text>
                )}
              </div>
              {msg.role === 'user' && (
                <Avatar
                  icon={<UserOutlined />}
                  className="chunk-avatar user-avatar"
                  style={{ backgroundColor: '#52c41a' }}
                />
              )}
            </div>
          </div>
        ))}

        {/* 显示当前流式传输的AI回答 */}
        {currentStreamingContent && (
          <div className="chunk-wrapper ai-chunk">
            <div className="chunk-content">
              <Avatar
                icon={<RobotOutlined />}
                className="chunk-avatar ai-avatar"
                style={{ backgroundColor: '#1890ff' }}
              />
              <div className="chunk-bubble">
                {/* <Markdown>{currentStreamingContent}</Markdown> */}
                <AIMarkdownRender
                  content={currentStreamingContent}
                ></AIMarkdownRender>
              </div>
            </div>
          </div>
        )}

        {/* 显示加载状态 */}
        {responding && !currentStreamingContent && (
          <div className="chunk-wrapper ai-chunk">
            <div className="chunk-content">
              <Avatar
                icon={<RobotOutlined />}
                className="chunk-avatar ai-avatar"
                style={{ backgroundColor: '#1890ff' }}
              />
              <div className="chunk-bubble loading-bubble">
                <Space>
                  <Spin size="small" />
                  <Text type="secondary">AI正在思考中...</Text>
                </Space>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      <div className="input-container">
        <div className="input-wrapper">
          <div className="chat-input-container">
            <Input.TextArea
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="请输入您的问题..."
              className="chat-input"
              autoSize={{ minRows: 1, maxRows: 4 }}
            />
            <Button
              type="primary"
              icon={<SendOutlined />}
              onClick={handleSend}
              disabled={!inputValue.trim() || responding}
              className="send-button"
            />
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <div className="ai-page">
      {messageAll.length === 0 && !currentStreamingContent
        ? renderInitialState()
        : renderChatState()}

      <ModelConfigModal
        visible={configModalVisible}
        onCancel={() => setConfigModalVisible(false)}
        onSave={handleConfigSave}
        initialConfig={modelConfig}
      />
    </div>
  )
}

export default AIComponent
