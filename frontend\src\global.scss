/* Global styles for HTTP method colors */
.http-method {
    font-weight: 500;
    display: inline-block;
    text-align: center;

    &.get {
        color: #2db7f5 !important; /* Blue */
    }

    &.post {
        color: #ff9800 !important; /* Orange */
    }

    &.put {
        color: #4caf50 !important; /* Green */
    }

    &.delete {
        color: #f44336 !important; /* Red */
    }

    &.options {
        color: #1890ff !important; /* Light Blue */
    }

    &.head {
        color: #722ed1 !important; /* Purple */
    }

    &.patch {
        color: #eb2f96 !important; /* Pink */
    }

    &.trace {
        color: #7265e6 !important; /* Indigo */
    }
}

/* Style for the dropdown items */
.ant-select-dropdown {
    .ant-select-item {
        padding: 8px 12px !important;
        text-align: center !important;

        .ant-select-item-option-content {
            display: flex !important;
            justify-content: center !important;
            text-align: center !important;

            .http-method {
                font-size: 14px !important;
                font-weight: 600 !important;
            }
        }
    }
}

/* Style for the selected item in the dropdown */
.ant-select-selection-item {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

/* Monaco Editor Styles for better handling of long text - 强制换行显示，无横向滚动 */
.monaco-editor {
    .view-line {
        word-wrap: break-word !important;
        white-space: pre-wrap !important;
        overflow-wrap: break-word !important;
    }

    .monaco-scrollable-element {
        overflow: hidden !important;
    }

    .overflow-guard {
        overflow: hidden !important;
    }

    /* 隐藏所有滚动条 */
    .scrollbar {
        display: none !important;
    }

    .scrollbar.horizontal,
    .scrollbar.vertical {
        display: none !important;
        width: 0 !important;
        height: 0 !important;
        opacity: 0 !important;
        visibility: hidden !important;
    }

    .view-lines {
        width: 100% !important;
    }

    .lines-content {
        position: relative;
        width: 100% !important;
    }
}
