export namespace httpClient {
	
	export class HTTPParam {
	    id: number;
	    key: string;
	    value: string;
	
	    static createFrom(source: any = {}) {
	        return new HTTPParam(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.id = source["id"];
	        this.key = source["key"];
	        this.value = source["value"];
	    }
	}
	export class HTTPRequest {
	    method: string;
	    url: string;
	    headers: Record<string, string>;
	    params: HTTPParam[];
	    body: string;
	    files?: Record<string, string>;
	
	    static createFrom(source: any = {}) {
	        return new HTTPRequest(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.method = source["method"];
	        this.url = source["url"];
	        this.headers = source["headers"];
	        this.params = this.convertValues(source["params"], HTTPParam);
	        this.body = source["body"];
	        this.files = source["files"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class HTTPResponse {
	    status: number;
	    statusText: string;
	    headers: Record<string, string>;
	    body: string;
	    size: number;
	    time: number;
	    contentType: string;
	    error?: string;
	
	    static createFrom(source: any = {}) {
	        return new HTTPResponse(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.status = source["status"];
	        this.statusText = source["statusText"];
	        this.headers = source["headers"];
	        this.body = source["body"];
	        this.size = source["size"];
	        this.time = source["time"];
	        this.contentType = source["contentType"];
	        this.error = source["error"];
	    }
	}

}

export namespace schema {
	
	export class ChatMessageAudioURL {
	    url?: string;
	    uri?: string;
	    mime_type?: string;
	    extra?: Record<string, any>;
	
	    static createFrom(source: any = {}) {
	        return new ChatMessageAudioURL(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.url = source["url"];
	        this.uri = source["uri"];
	        this.mime_type = source["mime_type"];
	        this.extra = source["extra"];
	    }
	}
	export class ChatMessageFileURL {
	    url?: string;
	    uri?: string;
	    mime_type?: string;
	    name?: string;
	    extra?: Record<string, any>;
	
	    static createFrom(source: any = {}) {
	        return new ChatMessageFileURL(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.url = source["url"];
	        this.uri = source["uri"];
	        this.mime_type = source["mime_type"];
	        this.name = source["name"];
	        this.extra = source["extra"];
	    }
	}
	export class ChatMessageImageURL {
	    url?: string;
	    uri?: string;
	    detail?: string;
	    mime_type?: string;
	    extra?: Record<string, any>;
	
	    static createFrom(source: any = {}) {
	        return new ChatMessageImageURL(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.url = source["url"];
	        this.uri = source["uri"];
	        this.detail = source["detail"];
	        this.mime_type = source["mime_type"];
	        this.extra = source["extra"];
	    }
	}
	export class ChatMessageVideoURL {
	    url?: string;
	    uri?: string;
	    mime_type?: string;
	    extra?: Record<string, any>;
	
	    static createFrom(source: any = {}) {
	        return new ChatMessageVideoURL(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.url = source["url"];
	        this.uri = source["uri"];
	        this.mime_type = source["mime_type"];
	        this.extra = source["extra"];
	    }
	}
	export class ChatMessagePart {
	    type?: string;
	    text?: string;
	    image_url?: ChatMessageImageURL;
	    audio_url?: ChatMessageAudioURL;
	    video_url?: ChatMessageVideoURL;
	    file_url?: ChatMessageFileURL;
	
	    static createFrom(source: any = {}) {
	        return new ChatMessagePart(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.type = source["type"];
	        this.text = source["text"];
	        this.image_url = this.convertValues(source["image_url"], ChatMessageImageURL);
	        this.audio_url = this.convertValues(source["audio_url"], ChatMessageAudioURL);
	        this.video_url = this.convertValues(source["video_url"], ChatMessageVideoURL);
	        this.file_url = this.convertValues(source["file_url"], ChatMessageFileURL);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	
	export class FunctionCall {
	    name?: string;
	    arguments?: string;
	
	    static createFrom(source: any = {}) {
	        return new FunctionCall(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.name = source["name"];
	        this.arguments = source["arguments"];
	    }
	}
	export class TopLogProb {
	    token: string;
	    logprob: number;
	    bytes?: number[];
	
	    static createFrom(source: any = {}) {
	        return new TopLogProb(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.token = source["token"];
	        this.logprob = source["logprob"];
	        this.bytes = source["bytes"];
	    }
	}
	export class LogProb {
	    token: string;
	    logprob: number;
	    bytes?: number[];
	    top_logprobs: TopLogProb[];
	
	    static createFrom(source: any = {}) {
	        return new LogProb(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.token = source["token"];
	        this.logprob = source["logprob"];
	        this.bytes = source["bytes"];
	        this.top_logprobs = this.convertValues(source["top_logprobs"], TopLogProb);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class LogProbs {
	    content: LogProb[];
	
	    static createFrom(source: any = {}) {
	        return new LogProbs(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.content = this.convertValues(source["content"], LogProb);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class TokenUsage {
	    prompt_tokens: number;
	    completion_tokens: number;
	    total_tokens: number;
	
	    static createFrom(source: any = {}) {
	        return new TokenUsage(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.prompt_tokens = source["prompt_tokens"];
	        this.completion_tokens = source["completion_tokens"];
	        this.total_tokens = source["total_tokens"];
	    }
	}
	export class ResponseMeta {
	    finish_reason?: string;
	    usage?: TokenUsage;
	    logprobs?: LogProbs;
	
	    static createFrom(source: any = {}) {
	        return new ResponseMeta(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.finish_reason = source["finish_reason"];
	        this.usage = this.convertValues(source["usage"], TokenUsage);
	        this.logprobs = this.convertValues(source["logprobs"], LogProbs);
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class ToolCall {
	    index?: number;
	    id: string;
	    type: string;
	    function: FunctionCall;
	    extra?: Record<string, any>;
	
	    static createFrom(source: any = {}) {
	        return new ToolCall(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.index = source["index"];
	        this.id = source["id"];
	        this.type = source["type"];
	        this.function = this.convertValues(source["function"], FunctionCall);
	        this.extra = source["extra"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	export class Message {
	    role: string;
	    content: string;
	    multi_content?: ChatMessagePart[];
	    name?: string;
	    tool_calls?: ToolCall[];
	    tool_call_id?: string;
	    tool_name?: string;
	    response_meta?: ResponseMeta;
	    extra?: Record<string, any>;
	
	    static createFrom(source: any = {}) {
	        return new Message(source);
	    }
	
	    constructor(source: any = {}) {
	        if ('string' === typeof source) source = JSON.parse(source);
	        this.role = source["role"];
	        this.content = source["content"];
	        this.multi_content = this.convertValues(source["multi_content"], ChatMessagePart);
	        this.name = source["name"];
	        this.tool_calls = this.convertValues(source["tool_calls"], ToolCall);
	        this.tool_call_id = source["tool_call_id"];
	        this.tool_name = source["tool_name"];
	        this.response_meta = this.convertValues(source["response_meta"], ResponseMeta);
	        this.extra = source["extra"];
	    }
	
		convertValues(a: any, classs: any, asMap: boolean = false): any {
		    if (!a) {
		        return a;
		    }
		    if (a.slice && a.map) {
		        return (a as any[]).map(elem => this.convertValues(elem, classs));
		    } else if ("object" === typeof a) {
		        if (asMap) {
		            for (const key of Object.keys(a)) {
		                a[key] = new classs(a[key]);
		            }
		            return a;
		        }
		        return new classs(a);
		    }
		    return a;
		}
	}
	
	
	

}

