import { Input, InputRef } from 'antd'
import React from 'react'

interface SearchProps {
  // 搜索方法
  onSearch: (value: string) => void
  // 设置搜索值
  setSearchTerm: (value: string) => void
  // 搜索框ref
  searchRef: React.RefObject<InputRef>
}

const SearchComponent: React.FC<SearchProps> = ({
  onSearch,
  setSearchTerm,
  searchRef,
}) => {
  return (
    <>
      <Input.Search
        ref={searchRef}
        style={{ width: '50%', marginRight: '10px' }}
        placeholder="请输入检索内容, 多字段检索请以`逗号`分隔."
        onChange={(e) => {
          if (e.target.value === '') {
            setSearchTerm('')
          }
        }}
        onSearch={onSearch}
        enterButton
        allowClear
      />
    </>
  )
}

export default SearchComponent
