import { useEffect, useRef } from 'react'

/**
 * 监听指定 DOM 元素的 scroll 事件
 * @param {string} elementId - 要监听的 DOM 元素的 id
 * @param {function} onScroll - 滚动时执行的回调函数，接收 scrollTop 参数
 */
export function useScrollListener(
  elementId: string,
  onScroll: (scrollTop: number, clientHeight: number) => void,
  debounceTime = 100,
) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    const element = document.getElementById(elementId)

    if (!element) {
      console.warn(`Element with id "${elementId}" not found.`)
      return
    }

    const handleScroll = () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      timeoutRef.current = setTimeout(() => {
        if (onScroll && typeof onScroll === 'function') {
          onScroll(element.scrollTop, element.clientHeight)
        }
      }, debounceTime)
    }

    element.addEventListener('scroll', handleScroll)

    return () => {
      element.removeEventListener('scroll', handleScroll)
      if (timeoutRef.current) clearTimeout(timeoutRef.current)
    }
  }, [elementId, onScroll, debounceTime])
}
