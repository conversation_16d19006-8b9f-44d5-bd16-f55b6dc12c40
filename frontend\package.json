{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@modelcontextprotocol/sdk": "^1.7.0", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@types/uuid": "^10.0.0", "@xterm/addon-fit": "^0.10.0", "antd": "^5.22.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.4.7", "hast": "^1.0.0", "katex": "^0.16.22", "lucide-react": "^0.518.0", "openai": "^4.87.3", "prismjs": "^1.29.0", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-simple-code-editor": "^0.14.1", "react-syntax-highlighter": "^15.6.1", "react-virtualized": "^9.22.5", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "sass": "^1.83.0", "strip-ansi": "^7.1.0", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "unist-util-visit": "^5.0.0", "uuid": "^11.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.15.0", "@tailwindcss/postcss": "^4.1.10", "@types/hast": "^3.0.4", "@types/node": "^22.10.0", "@types/node-sass": "^4.11.8", "@types/prismjs": "^1.26.5", "@types/react": "^18.2.0", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "^18.2.0", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-virtualized": "^9.22.0", "@typescript-eslint/eslint-plugin": "^8.16.0", "@typescript-eslint/parser": "^8.16.0", "@vitejs/plugin-react": "^2.2.0", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "globals": "^15.12.0", "postcss": "^8.5.6", "prettier": "^3.4.1", "prettier-eslint": "^16.3.0", "tailwindcss": "^4.1.10", "typescript": "^4.6.4", "typescript-eslint": "^8.16.0", "vite": "^3.2.11"}}