// Polyfill for Node.js URL utilities in browser environment

/**
 * Check if a string is a valid URL
 * @param {string} string - The string to check
 * @returns {boolean} - True if the string is a valid URL
 */
export function isUrl(string) {
  try {
    new URL(string)
    return true
  } catch {
    return false
  }
}

/**
 * Convert a file URL to a path
 * @param {string|URL} url - The URL to convert
 * @returns {string} - The path
 */
export function urlToPath(url) {
  if (typeof url === 'string') {
    url = new URL(url)
  }
  
  if (url.protocol !== 'file:') {
    throw new Error('URL must be a file: URL')
  }
  
  return decodeURIComponent(url.pathname)
}
