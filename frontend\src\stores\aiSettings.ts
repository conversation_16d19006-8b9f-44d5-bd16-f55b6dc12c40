import { create } from 'zustand'
import { ModelConfig } from '../types'

interface SettingsStore {
  config: ModelConfig

  updateConfig: (config: Partial<ModelConfig>) => void
}

const useSettingsStore = create<SettingsStore>((set) => ({
  config: {
    provider: 'ollama',
    apiUrl: 'http://192.100.8.27:11434',
    apiKey: '',
    model: '',
  },
  updateConfig: (config) =>
    set((state) => ({ config: { ...state.config, ...config } })),
}))

function updateConfig(config: Partial<ModelConfig>) {
  useSettingsStore.getState().updateConfig(config)
}

export { useSettingsStore, updateConfig }
